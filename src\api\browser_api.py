"""
浏览器API - 提供浏览器控制相关的接口
包括浏览器状态查询、导航控制等功能
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from loguru import logger

from ..models.openai_models import BrowserStatus, ErrorResponse
from ..browser_controller import BrowserManager, InputHandler, ResponseMonitor
from config import settings

router = APIRouter()

# 全局浏览器管理器实例
browser_manager: Optional[BrowserManager] = None


class NavigateRequest(BaseModel):
    """
    导航请求模型
    
    使用思路：
    1. 控制浏览器导航到指定URL
    2. 为AI交互准备页面环境
    
    使用例子：
    {
        "url": "https://chat.openai.com",
        "wait_for_load": true
    }
    """
    url: str
    wait_for_load: bool = True


class InputRequest(BaseModel):
    """
    输入请求模型
    
    使用思路：
    1. 直接控制浏览器输入
    2. 用于测试和调试
    
    使用例子：
    {
        "text": "Hello World",
        "selector": "#input-box",
        "submit": true
    }
    """
    text: str
    selector: Optional[str] = None
    submit: bool = False


def get_browser_manager():
    """获取浏览器管理器实例"""
    global browser_manager
    
    if not browser_manager:
        browser_manager = BrowserManager()
        if not browser_manager.start_browser():
            raise HTTPException(status_code=500, detail="浏览器启动失败")
        logger.info("浏览器管理器初始化完成")
    
    return browser_manager


@router.get("/browser/status", response_model=BrowserStatus)
async def get_browser_status():
    """
    获取浏览器状态
    
    返回: 浏览器当前状态信息
    
    使用思路：
    1. 检查浏览器是否正常运行
    2. 获取当前页面信息
    3. 用于健康检查和监控
    
    使用例子：
    GET /browser/status
    返回: {"running": true, "current_url": "https://example.com", ...}
    """
    try:
        browser_mgr = get_browser_manager()
        
        running = browser_mgr.is_browser_running()
        current_url = None
        
        if running and browser_mgr.driver:
            try:
                current_url = browser_mgr.driver.current_url
            except Exception as e:
                logger.warning(f"获取当前URL失败: {e}")
        
        status = BrowserStatus(
            running=running,
            current_url=current_url,
            browser_type=settings.browser.browser_type,
            headless=settings.browser.browser_headless,
            last_activity=None  # 可以添加活动时间跟踪
        )
        
        return status
        
    except Exception as e:
        logger.error(f"获取浏览器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.post("/browser/navigate")
async def navigate_browser(request: NavigateRequest):
    """
    导航浏览器到指定URL
    
    参数: request - 导航请求
    返回: 导航结果
    
    使用思路：
    1. 控制浏览器访问指定网站
    2. 为AI交互准备页面环境
    3. 支持等待页面加载完成
    
    使用例子：
    POST /browser/navigate
    {"url": "https://chat.openai.com", "wait_for_load": true}
    """
    try:
        browser_mgr = get_browser_manager()
        
        if not browser_mgr.is_browser_running():
            raise HTTPException(status_code=500, detail="浏览器未运行")
        
        success = browser_mgr.navigate_to(request.url)
        
        if not success:
            raise HTTPException(status_code=500, detail="导航失败")
        
        # 如果需要等待页面加载
        if request.wait_for_load:
            import time
            time.sleep(3)  # 简单等待，实际应用中可以使用更智能的等待策略
        
        return {"success": True, "url": request.url, "message": "导航成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"浏览器导航失败: {e}")
        raise HTTPException(status_code=500, detail=f"导航失败: {str(e)}")


@router.post("/browser/input")
async def browser_input(request: InputRequest):
    """
    在浏览器中输入文本
    
    参数: request - 输入请求
    返回: 输入结果
    
    使用思路：
    1. 直接控制浏览器输入
    2. 用于测试和调试
    3. 支持自动提交
    
    使用例子：
    POST /browser/input
    {"text": "Hello", "selector": "#input", "submit": true}
    """
    try:
        browser_mgr = get_browser_manager()
        input_handler = InputHandler(browser_mgr)
        
        if not browser_mgr.is_browser_running():
            raise HTTPException(status_code=500, detail="浏览器未运行")
        
        if request.submit:
            success = input_handler.input_and_submit(request.text, request.selector)
        else:
            success = input_handler.input_text(request.text, request.selector)
        
        if not success:
            raise HTTPException(status_code=500, detail="输入失败")
        
        return {
            "success": True, 
            "text": request.text, 
            "submitted": request.submit,
            "message": "输入成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"浏览器输入失败: {e}")
        raise HTTPException(status_code=500, detail=f"输入失败: {str(e)}")


@router.post("/browser/restart")
async def restart_browser():
    """
    重启浏览器
    
    返回: 重启结果
    
    使用思路：
    1. 关闭当前浏览器实例
    2. 重新启动浏览器
    3. 用于故障恢复
    
    使用例子：
    POST /browser/restart
    返回: {"success": true, "message": "浏览器重启成功"}
    """
    try:
        global browser_manager
        
        # 关闭现有浏览器
        if browser_manager:
            browser_manager.close_browser()
            browser_manager = None
        
        # 重新启动
        browser_mgr = get_browser_manager()
        
        return {"success": True, "message": "浏览器重启成功"}
        
    except Exception as e:
        logger.error(f"浏览器重启失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启失败: {str(e)}")


@router.get("/browser/page-source")
async def get_page_source():
    """
    获取当前页面源码
    
    返回: 页面源码
    
    使用思路：
    1. 获取当前页面的HTML源码
    2. 用于调试和分析页面结构
    
    使用例子：
    GET /browser/page-source
    返回: {"source": "<html>...</html>"}
    """
    try:
        browser_mgr = get_browser_manager()
        
        if not browser_mgr.is_browser_running():
            raise HTTPException(status_code=500, detail="浏览器未运行")
        
        source = browser_mgr.get_page_source()
        
        if source is None:
            raise HTTPException(status_code=500, detail="获取页面源码失败")
        
        return {"source": source}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取页面源码失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")
