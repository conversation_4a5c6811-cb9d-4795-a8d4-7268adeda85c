# Browser AI - 浏览器控制AI接口

基于浏览器自动化的AI接口服务，提供OpenAI兼容的聊天完成API。通过控制浏览器与AI网站交互，实现AI问答功能。

## 🚀 功能特性

- **OpenAI兼容API**: 提供标准的 `/v1/chat/completions` 接口
- **浏览器自动化**: 支持Chrome、Firefox、Edge等主流浏览器
- **智能输入控制**: 自动识别输入框并处理用户问题
- **响应监听**: 实时监听AI回复并返回给客户端
- **灵活配置**: 支持有头/无头模式、超时设置等
- **健康检查**: 提供服务状态监控接口
- **详细日志**: 完整的操作日志记录

## 📋 系统要求

- Python 3.8+
- Chrome/Firefox/Edge 浏览器
- Windows/Linux/macOS

## 🛠️ 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd browerAi
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境

#### 自动配置（推荐）
使用配置工具自动检测浏览器并生成配置：
```bash
python config_tool.py
```

#### 手动配置
如果自动配置失败，可以手动编辑 `.env` 文件：
```bash
# 基本配置
BROWSER__BROWSER_TYPE=chrome
BROWSER__BROWSER_HEADLESS=false

# 浏览器路径配置（当自动检测失败时）
BROWSER__CHROME_BINARY_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
BROWSER__FIREFOX_BINARY_PATH=C:\Program Files\Mozilla Firefox\firefox.exe
BROWSER__EDGE_BINARY_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe
```

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）
```bash
# 使用默认配置启动
python start.py

# 指定端口启动
python start.py --port 8080

# 无头模式启动
python start.py --headless

# 使用Firefox浏览器
python start.py --browser firefox

# 调试模式
python start.py --debug
```

### 方式二：直接启动
```bash
python main.py
```

### 方式三：检查依赖
```bash
python start.py --check-deps
```

## 📖 API使用

### 聊天完成接口
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "什么是人工智能？"}
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

### 浏览器控制接口
```bash
# 检查浏览器状态
curl "http://localhost:8000/browser/status"

# 导航到指定网站
curl -X POST "http://localhost:8000/browser/navigate" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://chat.openai.com"}'

# 直接输入文本
curl -X POST "http://localhost:8000/browser/input" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello World", "submit": true}'
```

### 健康检查
```bash
curl "http://localhost:8000/health"
```

## 🔧 配置说明

### 配置文件结构
项目支持多层次的配置系统：
- `.env` 文件：环境变量配置
- `config.py`：主配置文件
- `config_example.py`：配置示例和模板

### 主要配置选项

#### 浏览器配置
```env
# 基本浏览器配置
BROWSER__BROWSER_TYPE=chrome              # 浏览器类型
BROWSER__BROWSER_HEADLESS=false           # 是否无头模式
BROWSER__BROWSER_TIMEOUT=30               # 操作超时时间

# 浏览器路径配置（当自动检测失败时使用）
BROWSER__CHROME_BINARY_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
BROWSER__FIREFOX_BINARY_PATH=C:\Program Files\Mozilla Firefox\firefox.exe
BROWSER__EDGE_BINARY_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe

# 页面加载配置
BROWSER__PAGE_LOAD_TIMEOUT=30             # 页面加载超时
BROWSER__IMPLICIT_WAIT=10                 # 隐式等待时间
BROWSER__BROWSER_START_RETRY_COUNT=3      # 启动重试次数
```

#### 输入处理配置
```env
# 输入行为配置
INPUT__INPUT_DELAY=0.1                    # 输入字符间延迟
INPUT__CLEAR_BEFORE_INPUT=true            # 输入前清空
INPUT__INPUT_RETRY_COUNT=3                # 输入重试次数
```

#### 响应监听配置
```env
# 响应等待配置
RESPONSE__RESPONSE_TIMEOUT=60             # 响应超时时间
RESPONSE__RESPONSE_CHECK_INTERVAL=0.5     # 检查间隔
RESPONSE__RESPONSE_STABLE_TIME=2.0        # 响应稳定时间
RESPONSE__MIN_RESPONSE_LENGTH=5           # 最小响应长度
RESPONSE__CLEAN_RESPONSE_TEXT=true        # 清理响应文本
```

### 配置工具
使用配置工具自动检测和配置浏览器：
```bash
# 自动检测浏览器并生成配置
python config_tool.py

# 检查当前配置
python start.py --check-deps
```

### 命令行参数
```bash
python start.py --help
```

## 📁 项目结构

```
browerAi/
├── main.py                 # 主应用程序入口
├── start.py               # 启动脚本
├── config.py              # 配置管理
├── requirements.txt       # 依赖包列表
├── .env                   # 环境变量配置
├── README.md             # 项目说明
├── src/                  # 源代码目录
│   ├── api/              # API接口模块
│   │   ├── chat_api.py   # 聊天API
│   │   └── browser_api.py # 浏览器控制API
│   ├── browser_controller/ # 浏览器控制模块
│   │   ├── browser_manager.py    # 浏览器管理器
│   │   ├── input_handler.py      # 输入处理器
│   │   └── response_monitor.py   # 响应监听器
│   ├── models/           # 数据模型
│   │   └── openai_models.py # OpenAI兼容模型
│   └── utils/            # 工具模块
│       ├── logger_config.py     # 日志配置
│       └── ai_interaction.py    # AI交互管理器
└── tests/                # 测试目录
```

## 🔍 使用场景

### 1. 作为OpenAI API的替代
```python
import openai

# 配置API端点
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "dummy"  # 可以是任意值

# 使用标准OpenAI客户端
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "你好"}
    ]
)

print(response.choices[0].message.content)
```

### 2. 集成到现有项目
```python
import requests

def ask_ai(question):
    response = requests.post(
        "http://localhost:8000/v1/chat/completions",
        json={
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": question}]
        }
    )
    return response.json()["choices"][0]["message"]["content"]

answer = ask_ai("什么是机器学习？")
print(answer)
```

## 🐛 故障排除

### 常见问题

1. **浏览器启动失败**
   - 运行 `python config_tool.py` 自动检测浏览器路径
   - 手动在 `.env` 文件中设置 `BROWSER__CHROME_BINARY_PATH` 等路径
   - 尝试使用不同的浏览器类型：`python start.py --browser firefox`
   - 检查浏览器版本与驱动兼容性

2. **输入框找不到**
   - 确保目标网站已正确加载
   - 在 `.env` 中自定义输入框选择器
   - 参考 `config_example.py` 中针对不同AI网站的配置
   - 使用浏览器开发者工具查找正确的选择器

3. **响应超时或识别失败**
   - 增加 `RESPONSE__RESPONSE_TIMEOUT` 设置
   - 调整 `RESPONSE__RESPONSE_STABLE_TIME` 等待时间
   - 自定义响应选择器以适配特定网站
   - 检查网络连接和AI网站状态

4. **依赖包安装失败**
   - 使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - 更新pip版本：`pip install --upgrade pip`
   - 检查Python版本兼容性（需要3.8+）

5. **配置问题**
   - 运行 `python config_tool.py` 重新生成配置
   - 参考 `config_example.py` 查看完整配置示例
   - 检查 `.env` 文件编码（应为UTF-8）

### 日志查看
```bash
# 查看实时日志
tail -f browser_ai.log

# 查看错误日志
grep ERROR browser_ai.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Selenium](https://selenium.dev/) - 浏览器自动化框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [Loguru](https://loguru.readthedocs.io/) - 优雅的日志库
