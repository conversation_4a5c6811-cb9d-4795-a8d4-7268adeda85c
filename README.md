# Browser AI - 浏览器控制AI接口

基于浏览器自动化的AI接口服务，提供OpenAI兼容的聊天完成API。通过控制浏览器与AI网站交互，实现AI问答功能。

## 🚀 功能特性

- **OpenAI兼容API**: 提供标准的 `/v1/chat/completions` 接口
- **浏览器自动化**: 支持Chrome、Firefox、Edge等主流浏览器
- **智能输入控制**: 自动识别输入框并处理用户问题
- **响应监听**: 实时监听AI回复并返回给客户端
- **灵活配置**: 支持有头/无头模式、超时设置等
- **健康检查**: 提供服务状态监控接口
- **详细日志**: 完整的操作日志记录

## 📋 系统要求

- Python 3.8+
- Chrome/Firefox/Edge 浏览器
- Windows/Linux/macOS

## 🛠️ 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd browerAi
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境
复制 `.env` 文件并根据需要修改配置：
```bash
# 可选：修改 .env 文件中的配置
# API_HOST=0.0.0.0
# API_PORT=8000
# BROWSER_TYPE=chrome
# BROWSER_HEADLESS=false
```

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）
```bash
# 使用默认配置启动
python start.py

# 指定端口启动
python start.py --port 8080

# 无头模式启动
python start.py --headless

# 使用Firefox浏览器
python start.py --browser firefox

# 调试模式
python start.py --debug
```

### 方式二：直接启动
```bash
python main.py
```

### 方式三：检查依赖
```bash
python start.py --check-deps
```

## 📖 API使用

### 聊天完成接口
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "什么是人工智能？"}
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

### 浏览器控制接口
```bash
# 检查浏览器状态
curl "http://localhost:8000/browser/status"

# 导航到指定网站
curl -X POST "http://localhost:8000/browser/navigate" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://chat.openai.com"}'

# 直接输入文本
curl -X POST "http://localhost:8000/browser/input" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello World", "submit": true}'
```

### 健康检查
```bash
curl "http://localhost:8000/health"
```

## 🔧 配置说明

### 环境变量配置 (.env)
```env
# API服务配置
API_HOST=0.0.0.0          # API监听地址
API_PORT=8000             # API端口

# 浏览器配置
BROWSER_TYPE=chrome       # 浏览器类型 (chrome/firefox/edge)
BROWSER_HEADLESS=false    # 是否无头模式
BROWSER_TIMEOUT=30        # 操作超时时间(秒)
BROWSER_WINDOW_SIZE=1920,1080  # 窗口大小

# AI模型配置
MODEL_NAME=gpt-3.5-turbo  # 默认模型名称
MAX_TOKENS=2048           # 最大token数
TEMPERATURE=0.7           # 温度参数

# 日志配置
LOG_LEVEL=INFO            # 日志级别
LOG_FILE=browser_ai.log   # 日志文件

# 调试模式
DEBUG=false               # 是否启用调试模式
```

### 命令行参数
```bash
python start.py --help
```

## 📁 项目结构

```
browerAi/
├── main.py                 # 主应用程序入口
├── start.py               # 启动脚本
├── config.py              # 配置管理
├── requirements.txt       # 依赖包列表
├── .env                   # 环境变量配置
├── README.md             # 项目说明
├── src/                  # 源代码目录
│   ├── api/              # API接口模块
│   │   ├── chat_api.py   # 聊天API
│   │   └── browser_api.py # 浏览器控制API
│   ├── browser_controller/ # 浏览器控制模块
│   │   ├── browser_manager.py    # 浏览器管理器
│   │   ├── input_handler.py      # 输入处理器
│   │   └── response_monitor.py   # 响应监听器
│   ├── models/           # 数据模型
│   │   └── openai_models.py # OpenAI兼容模型
│   └── utils/            # 工具模块
│       ├── logger_config.py     # 日志配置
│       └── ai_interaction.py    # AI交互管理器
└── tests/                # 测试目录
```

## 🔍 使用场景

### 1. 作为OpenAI API的替代
```python
import openai

# 配置API端点
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "dummy"  # 可以是任意值

# 使用标准OpenAI客户端
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "你好"}
    ]
)

print(response.choices[0].message.content)
```

### 2. 集成到现有项目
```python
import requests

def ask_ai(question):
    response = requests.post(
        "http://localhost:8000/v1/chat/completions",
        json={
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": question}]
        }
    )
    return response.json()["choices"][0]["message"]["content"]

answer = ask_ai("什么是机器学习？")
print(answer)
```

## 🐛 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装对应的浏览器
   - 检查浏览器驱动是否正确安装
   - 尝试使用不同的浏览器类型

2. **输入框找不到**
   - 确保目标网站已正确加载
   - 检查网站的输入框选择器
   - 尝试手动指定输入框选择器

3. **响应超时**
   - 增加超时时间设置
   - 检查网络连接
   - 确认AI网站响应正常

4. **依赖包安装失败**
   - 使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - 更新pip版本：`pip install --upgrade pip`

### 日志查看
```bash
# 查看实时日志
tail -f browser_ai.log

# 查看错误日志
grep ERROR browser_ai.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Selenium](https://selenium.dev/) - 浏览器自动化框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [Loguru](https://loguru.readthedocs.io/) - 优雅的日志库
