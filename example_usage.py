"""
使用示例 - 演示如何使用Browser AI接口
包含各种使用场景和最佳实践
"""

import requests
import time
import json
from typing import Optional, Dict, Any


class BrowserAIClient:
    """Browser AI客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        参数: base_url - API服务地址
        
        使用思路：
        1. 封装API调用逻辑
        2. 提供简洁的使用接口
        3. 处理错误和重试
        
        使用例子：
        client = BrowserAIClient("http://localhost:8000")
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = 60
        
    def check_health(self) -> bool:
        """
        检查服务健康状态
        
        返回: 服务是否健康
        
        使用例子：
        if client.check_health():
            print("服务正常")
        """
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200 and response.json().get("status") == "healthy"
        except:
            return False
    
    def get_browser_status(self) -> Optional[Dict[str, Any]]:
        """
        获取浏览器状态
        
        返回: 浏览器状态信息或None
        
        使用例子：
        status = client.get_browser_status()
        if status and status['running']:
            print(f"浏览器正在运行: {status['current_url']}")
        """
        try:
            response = requests.get(f"{self.base_url}/browser/status", timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            print(f"获取浏览器状态失败: {e}")
        return None
    
    def navigate_to(self, url: str, wait_for_load: bool = True) -> bool:
        """
        导航浏览器到指定URL
        
        参数:
            url - 目标URL
            wait_for_load - 是否等待页面加载
        返回: 导航是否成功
        
        使用例子：
        success = client.navigate_to("https://chat.openai.com")
        if success:
            print("导航成功")
        """
        try:
            payload = {"url": url, "wait_for_load": wait_for_load}
            response = requests.post(
                f"{self.base_url}/browser/navigate",
                json=payload,
                timeout=self.timeout
            )
            return response.status_code == 200 and response.json().get("success", False)
        except Exception as e:
            print(f"导航失败: {e}")
            return False
    
    def input_text(self, text: str, selector: Optional[str] = None, submit: bool = False) -> bool:
        """
        在浏览器中输入文本
        
        参数:
            text - 要输入的文本
            selector - 输入框选择器
            submit - 是否提交
        返回: 输入是否成功
        
        使用例子：
        success = client.input_text("Hello World", submit=True)
        """
        try:
            payload = {"text": text, "submit": submit}
            if selector:
                payload["selector"] = selector
                
            response = requests.post(
                f"{self.base_url}/browser/input",
                json=payload,
                timeout=self.timeout
            )
            return response.status_code == 200 and response.json().get("success", False)
        except Exception as e:
            print(f"输入失败: {e}")
            return False
    
    def chat_completion(self, message: str, model: str = "gpt-3.5-turbo") -> Optional[str]:
        """
        发送聊天消息并获取AI回复
        
        参数:
            message - 用户消息
            model - 模型名称
        返回: AI回复内容或None
        
        使用例子：
        response = client.chat_completion("什么是人工智能？")
        if response:
            print(f"AI回复: {response}")
        """
        try:
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": message}],
                "temperature": 0.7,
                "max_tokens": 2048
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=120  # 聊天可能需要更长时间
            )
            
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                print(f"聊天请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"聊天失败: {e}")
        
        return None


def example_basic_usage():
    """
    基础使用示例
    
    使用思路：
    1. 演示基本的API调用流程
    2. 展示错误处理
    3. 提供使用模板
    """
    print("=" * 60)
    print("📖 基础使用示例")
    print("=" * 60)
    
    # 创建客户端
    client = BrowserAIClient()
    
    # 1. 检查服务状态
    print("1. 检查服务健康状态...")
    if not client.check_health():
        print("❌ 服务不可用，请确保服务已启动")
        return
    print("✅ 服务正常")
    
    # 2. 检查浏览器状态
    print("\n2. 检查浏览器状态...")
    status = client.get_browser_status()
    if status:
        print(f"✅ 浏览器状态: 运行中={status['running']}, 类型={status['browser_type']}")
    else:
        print("❌ 无法获取浏览器状态")
        return
    
    # 3. 导航到测试网站
    print("\n3. 导航到测试网站...")
    test_url = "https://www.baidu.com"
    if client.navigate_to(test_url):
        print(f"✅ 成功导航到: {test_url}")
    else:
        print(f"❌ 导航失败: {test_url}")
        return
    
    # 4. 测试输入功能
    print("\n4. 测试输入功能...")
    if client.input_text("Browser AI 测试", submit=False):
        print("✅ 文本输入成功")
    else:
        print("❌ 文本输入失败")
    
    print("\n✅ 基础功能测试完成")


def example_ai_chat():
    """
    AI聊天示例
    
    使用思路：
    1. 演示完整的AI对话流程
    2. 展示多轮对话
    3. 处理各种响应情况
    
    注意：需要先导航到支持的AI网站
    """
    print("=" * 60)
    print("🤖 AI聊天示例")
    print("=" * 60)
    
    client = BrowserAIClient()
    
    # 检查服务状态
    if not client.check_health():
        print("❌ 服务不可用")
        return
    
    # 示例问题列表
    questions = [
        "你好，请简单介绍一下自己",
        "什么是人工智能？",
        "Python有哪些优势？",
        "如何学习编程？"
    ]
    
    print("注意：此示例需要先导航到支持的AI网站（如ChatGPT、Claude等）")
    print("当前将演示API调用，但可能需要手动导航到AI网站")
    
    for i, question in enumerate(questions, 1):
        print(f"\n{i}. 问题: {question}")
        
        # 发送问题并获取回复
        response = client.chat_completion(question)
        
        if response:
            print(f"   回复: {response[:200]}...")
            if len(response) > 200:
                print("   (回复已截断)")
        else:
            print("   ❌ 未收到回复")
        
        # 短暂等待，避免请求过于频繁
        time.sleep(2)
    
    print("\n✅ AI聊天示例完成")


def example_openai_compatible():
    """
    OpenAI兼容性示例
    
    使用思路：
    1. 演示如何替换OpenAI API
    2. 展示兼容性
    3. 提供迁移指南
    """
    print("=" * 60)
    print("🔄 OpenAI兼容性示例")
    print("=" * 60)
    
    # 模拟使用OpenAI客户端的方式
    api_base = "http://localhost:8000/v1"
    
    def call_chat_api(messages, model="gpt-3.5-turbo"):
        """模拟OpenAI客户端调用"""
        try:
            response = requests.post(
                f"{api_base}/chat/completions",
                json={
                    "model": model,
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                timeout=120
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"API调用异常: {e}")
            return None
    
    # 示例对话
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手"},
        {"role": "user", "content": "请解释什么是RESTful API"}
    ]
    
    print("发送请求到OpenAI兼容接口...")
    print(f"API端点: {api_base}/chat/completions")
    print(f"消息: {messages[-1]['content']}")
    
    result = call_chat_api(messages)
    
    if result:
        print("\n✅ 成功收到响应:")
        print(f"模型: {result.get('model', 'unknown')}")
        print(f"回复: {result['choices'][0]['message']['content'][:200]}...")
        
        if 'usage' in result:
            usage = result['usage']
            print(f"Token使用: {usage.get('total_tokens', 0)} (输入: {usage.get('prompt_tokens', 0)}, 输出: {usage.get('completion_tokens', 0)})")
    else:
        print("❌ 请求失败")
    
    print("\n✅ OpenAI兼容性测试完成")


def example_error_handling():
    """
    错误处理示例
    
    使用思路：
    1. 演示各种错误情况的处理
    2. 展示重试机制
    3. 提供调试技巧
    """
    print("=" * 60)
    print("⚠️ 错误处理示例")
    print("=" * 60)
    
    client = BrowserAIClient()
    
    # 1. 服务不可用的情况
    print("1. 测试服务不可用情况...")
    offline_client = BrowserAIClient("http://localhost:9999")  # 错误的端口
    if not offline_client.check_health():
        print("✅ 正确检测到服务不可用")
    
    # 2. 无效URL导航
    print("\n2. 测试无效URL导航...")
    if not client.navigate_to("invalid-url"):
        print("✅ 正确处理无效URL")
    
    # 3. 超时处理
    print("\n3. 测试超时处理...")
    # 这里可以设置一个很短的超时时间来模拟超时
    client.timeout = 1  # 1秒超时
    result = client.chat_completion("这是一个测试消息")
    if result is None:
        print("✅ 正确处理超时情况")
    
    # 恢复正常超时
    client.timeout = 60
    
    print("\n✅ 错误处理测试完成")


def main():
    """
    主函数 - 运行所有示例
    
    使用思路：
    1. 按顺序执行各个示例
    2. 提供交互式选择
    3. 处理用户中断
    """
    print("🚀 Browser AI 使用示例")
    print("请确保Browser AI服务已启动 (python start.py)")
    
    examples = [
        ("基础使用示例", example_basic_usage),
        ("AI聊天示例", example_ai_chat),
        ("OpenAI兼容性示例", example_openai_compatible),
        ("错误处理示例", example_error_handling)
    ]
    
    try:
        print("\n可用的示例:")
        for i, (name, _) in enumerate(examples, 1):
            print(f"{i}. {name}")
        print("0. 运行所有示例")
        
        choice = input("\n请选择要运行的示例 (0-4): ").strip()
        
        if choice == "0":
            # 运行所有示例
            for name, func in examples:
                print(f"\n{'='*20} {name} {'='*20}")
                func()
                input("\n按回车键继续下一个示例...")
        elif choice.isdigit() and 1 <= int(choice) <= len(examples):
            # 运行指定示例
            name, func = examples[int(choice) - 1]
            print(f"\n{'='*20} {name} {'='*20}")
            func()
        else:
            print("无效的选择")
            return
        
        print("\n🎉 示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n👋 示例被用户中断")
    except Exception as e:
        print(f"\n💥 运行示例时发生错误: {e}")


if __name__ == "__main__":
    main()
