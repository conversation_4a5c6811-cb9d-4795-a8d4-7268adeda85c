"""
日志配置模块
统一配置项目的日志系统
"""

import sys
from loguru import logger
from config import settings


def setup_logger():
    """
    设置日志配置
    
    使用思路：
    1. 移除默认的日志处理器
    2. 添加控制台和文件日志处理器
    3. 根据配置设置日志级别和格式
    
    使用例子：
    from src.utils import setup_logger
    setup_logger()
    logger.info("日志系统已初始化")
    """
    # 移除默认处理器
    logger.remove()
    
    # 日志格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=log_format,
        level=settings.log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    logger.add(
        settings.log_file,
        format=log_format,
        level=settings.log_level,
        rotation="10 MB",  # 日志文件大小达到10MB时轮转
        retention="7 days",  # 保留7天的日志文件
        compression="zip",  # 压缩旧日志文件
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")
    logger.info(f"日志级别: {settings.log_level}")
    logger.info(f"日志文件: {settings.log_file}")


def get_logger(name: str = None):
    """
    获取指定名称的日志器
    
    参数: name - 日志器名称，默认为None
    返回: 日志器实例
    
    使用思路：
    1. 为不同模块提供独立的日志器
    2. 便于日志追踪和调试
    
    使用例子：
    from src.utils.logger_config import get_logger
    logger = get_logger(__name__)
    logger.info("模块日志")
    """
    if name:
        return logger.bind(name=name)
    return logger
