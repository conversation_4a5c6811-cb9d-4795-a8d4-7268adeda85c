"""
测试浏览器修复后的启动情况
"""

import sys
import os
import time
from loguru import logger

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.browser_controller.browser_manager import <PERSON>rowserManager
from config import settings


def test_browser_startup():
    """测试浏览器启动"""
    print("🚀 测试浏览器启动修复")
    print("=" * 50)
    
    # 显示当前配置
    print(f"浏览器类型: {settings.browser.browser_type}")
    print(f"Chrome路径: {settings.browser.chrome_binary_path}")
    print(f"无头模式: {settings.browser.browser_headless}")
    print(f"启动参数数量: {len(settings.browser.chrome_options)}")
    
    print("\n正在创建浏览器管理器...")
    browser_manager = BrowserManager()
    
    try:
        print("\n正在启动浏览器...")
        start_time = time.time()
        
        success = browser_manager.start_browser()
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        if success:
            print(f"✅ 浏览器启动成功！耗时: {startup_time:.2f}秒")
            
            # 测试基本功能
            print("\n正在测试基本功能...")
            
            # 测试导航
            if browser_manager.navigate_to("https://www.baidu.com"):
                print("✅ 导航测试成功")
                
                # 获取页面标题
                if browser_manager.driver:
                    title = browser_manager.driver.title
                    print(f"✅ 页面标题: {title}")
                    
                    # 获取当前URL
                    current_url = browser_manager.driver.current_url
                    print(f"✅ 当前URL: {current_url}")
                    
            else:
                print("⚠️ 导航测试失败")
            
            # 测试状态检查
            is_running = browser_manager.is_browser_running()
            print(f"✅ 浏览器运行状态: {is_running}")
            
            print("\n🎉 所有测试通过！")
            return True
            
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        print(f"   错误类型: {type(e).__name__}")
        return False
        
    finally:
        # 清理资源
        try:
            if browser_manager.driver:
                print("\n正在关闭浏览器...")
                browser_manager.driver.quit()
                print("✅ 浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时发生错误: {e}")


def main():
    """主函数"""
    try:
        success = test_browser_startup()
        
        if success:
            print("\n🎊 浏览器修复测试成功！")
            print("\n💡 现在可以启动Browser AI服务:")
            print("python start.py --port 8002 --headless")
        else:
            print("\n⚠️ 浏览器修复测试失败")
            print("\n🔧 建议:")
            print("1. 检查Chrome浏览器安装")
            print("2. 确认Chrome路径配置正确")
            print("3. 重启计算机后重试")
            print("4. 检查系统防火墙设置")
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")


if __name__ == "__main__":
    main()
