<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser AI 对话测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            color: #333;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #667eea;
            order: 1;
        }

        .message.assistant .message-avatar {
            background: #4CAF50;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
        }

        .loading-dots {
            display: inline-flex;
            gap: 2px;
        }

        .loading-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #666;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .config-panel {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            font-size: 12px;
            color: #666;
        }

        .config-item {
            display: inline-block;
            margin-right: 15px;
        }

        .config-item strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator" id="statusIndicator"></div>
            <h1>🤖 Browser AI 对话测试</h1>
            <p>测试 OpenAI 兼容接口 - 通过浏览器控制与AI对话</p>
        </div>
        
        <div class="config-panel" id="configPanel">
            <div class="config-item"><strong>状态:</strong> <span id="serviceStatus">检查中...</span></div>
            <div class="config-item"><strong>浏览器:</strong> <span id="browserStatus">未知</span></div>
            <div class="config-item"><strong>API地址:</strong> <span id="apiEndpoint">http://localhost:8001</span></div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    👋 欢迎使用 Browser AI 对话测试！<br><br>
                    这个界面可以测试 OpenAI 兼容的聊天接口。请注意：<br>
                    • 首次使用需要先导航到AI网站<br>
                    • 确保浏览器已正确配置<br>
                    • 输入框和响应选择器需要匹配目标网站<br><br>
                    现在就开始对话吧！
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="输入您的消息..." 
                    autocomplete="off"
                >
                <button type="submit" class="send-button" id="sendButton">发送</button>
            </form>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8001';
        
        // DOM 元素
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatForm = document.getElementById('chatForm');
        const statusIndicator = document.getElementById('statusIndicator');
        const serviceStatus = document.getElementById('serviceStatus');
        const browserStatus = document.getElementById('browserStatus');

        // 状态管理
        let isLoading = false;
        let conversationHistory = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();
            messageInput.focus();
        });

        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    serviceStatus.textContent = '✅ 正常';
                    statusIndicator.style.background = '#4CAF50';
                    
                    // 检查浏览器状态
                    checkBrowserStatus();
                } else {
                    serviceStatus.textContent = '❌ 异常';
                    statusIndicator.style.background = '#f44336';
                }
            } catch (error) {
                serviceStatus.textContent = '❌ 连接失败';
                statusIndicator.style.background = '#f44336';
                console.error('Service check failed:', error);
            }
        }

        // 检查浏览器状态
        async function checkBrowserStatus() {
            try {
                const response = await fetch(`${API_BASE}/browser/status`);
                const data = await response.json();
                
                if (data.running) {
                    browserStatus.textContent = `✅ ${data.browser_type} (${data.headless ? '无头' : '有头'})`;
                } else {
                    browserStatus.textContent = '⏳ 未启动';
                }
            } catch (error) {
                browserStatus.textContent = '❌ 检查失败';
                console.error('Browser check failed:', error);
            }
        }

        // 表单提交处理
        chatForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            // 添加用户消息
            addMessage('user', message);
            messageInput.value = '';
            
            // 发送到API
            await sendMessage(message);
        });

        // 添加消息到界面
        function addMessage(role, content, isLoading = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? '你' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isLoading) {
                contentDiv.innerHTML = `
                    <div class="loading">
                        <span>AI正在思考</span>
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                `;
            } else {
                contentDiv.textContent = content;
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageDiv;
        }

        // 发送消息到API
        async function sendMessage(message) {
            isLoading = true;
            sendButton.disabled = true;
            
            // 添加加载消息
            const loadingMessage = addMessage('assistant', '', true);
            
            try {
                // 构建对话历史
                conversationHistory.push({ role: 'user', content: message });
                
                const response = await fetch(`${API_BASE}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: conversationHistory,
                        temperature: 0.7,
                        max_tokens: 2048
                    })
                });

                // 移除加载消息
                loadingMessage.remove();

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    
                    // 添加AI回复
                    addMessage('assistant', aiResponse);
                    
                    // 更新对话历史
                    conversationHistory.push({ role: 'assistant', content: aiResponse });
                    
                    // 限制历史长度
                    if (conversationHistory.length > 20) {
                        conversationHistory = conversationHistory.slice(-20);
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
            } catch (error) {
                // 移除加载消息
                loadingMessage.remove();
                
                // 显示错误
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = `❌ 发送失败: ${error.message}`;
                chatMessages.appendChild(errorDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                console.error('Send message failed:', error);
            } finally {
                isLoading = false;
                sendButton.disabled = false;
                messageInput.focus();
            }
        }

        // 定期检查状态
        setInterval(checkServiceStatus, 30000); // 每30秒检查一次
    </script>
</body>
</html>
