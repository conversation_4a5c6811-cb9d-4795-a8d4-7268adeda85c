<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser AI 对话测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            color: #333;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #667eea;
            order: 1;
        }

        .message.assistant .message-avatar {
            background: #4CAF50;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
        }

        .loading-dots {
            display: inline-flex;
            gap: 2px;
        }

        .loading-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #666;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px 16px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .config-panel {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            font-size: 12px;
            color: #666;
        }

        .config-item {
            display: inline-block;
            margin-right: 15px;
        }

        .config-item strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator" id="statusIndicator"></div>
            <h1>🤖 Browser AI 对话测试</h1>
            <p>测试 OpenAI 兼容接口 - 通过浏览器控制与AI对话</p>
        </div>
        
        <div class="config-panel" id="configPanel">
            <div class="config-item"><strong>状态:</strong> <span id="serviceStatus">检查中...</span></div>
            <div class="config-item"><strong>浏览器:</strong> <span id="browserStatus">未知</span></div>
            <div class="config-item"><strong>AI网站:</strong> <span id="aiWebsiteStatus">未知</span></div>
            <div class="config-item"><strong>API地址:</strong> <span id="apiEndpoint">http://localhost:8001</span></div>
        </div>

        <div class="ai-website-panel" id="aiWebsitePanel" style="display: none;">
            <div style="padding: 15px; background: #e3f2fd; border-bottom: 1px solid #e1e5e9;">
                <div style="margin-bottom: 10px;">
                    <strong>🌐 AI网站选择:</strong>
                    <select id="aiWebsiteSelect" style="margin-left: 10px; padding: 5px; border-radius: 4px; border: 1px solid #ccc;">
                        <option value="">加载中...</option>
                    </select>
                    <button id="navigateButton" style="margin-left: 10px; padding: 5px 15px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">导航</button>
                </div>
                <div style="font-size: 12px; color: #666;">
                    💡 选择AI网站后点击"导航"按钮，系统会自动配置输入框和响应选择器
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    👋 欢迎使用 Browser AI 对话测试！<br><br>
                    这个界面可以测试 OpenAI 兼容的聊天接口。使用步骤：<br>
                    1. 🌐 在上方选择AI网站（ChatGPT、Claude等）并点击"导航"<br>
                    2. ✅ 等待导航完成，系统会自动配置选择器<br>
                    3. 💬 在下方输入框中发送消息开始对话<br><br>
                    系统会自动识别输入框和响应内容，让您轻松测试AI对话功能！
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="输入您的消息..." 
                    autocomplete="off"
                >
                <button type="submit" class="send-button" id="sendButton">发送</button>
            </form>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8001';
        
        // DOM 元素
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatForm = document.getElementById('chatForm');
        const statusIndicator = document.getElementById('statusIndicator');
        const serviceStatus = document.getElementById('serviceStatus');
        const browserStatus = document.getElementById('browserStatus');
        const aiWebsiteStatus = document.getElementById('aiWebsiteStatus');
        const aiWebsitePanel = document.getElementById('aiWebsitePanel');
        const aiWebsiteSelect = document.getElementById('aiWebsiteSelect');
        const navigateButton = document.getElementById('navigateButton');

        // 状态管理
        let isLoading = false;
        let conversationHistory = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkServiceStatus();
            loadAvailableAIWebsites();
            messageInput.focus();

            // 绑定导航按钮事件
            navigateButton.addEventListener('click', navigateToAIWebsite);
        });

        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    serviceStatus.textContent = '✅ 正常';
                    statusIndicator.style.background = '#4CAF50';
                    
                    // 检查浏览器状态和AI网站状态
                    checkBrowserStatus();
                    checkAIWebsiteStatus();
                } else {
                    serviceStatus.textContent = '❌ 异常';
                    statusIndicator.style.background = '#f44336';
                }
            } catch (error) {
                serviceStatus.textContent = '❌ 连接失败';
                statusIndicator.style.background = '#f44336';
                console.error('Service check failed:', error);
            }
        }

        // 检查浏览器状态
        async function checkBrowserStatus() {
            try {
                const response = await fetch(`${API_BASE}/browser/status`);
                const data = await response.json();
                
                if (data.running) {
                    browserStatus.textContent = `✅ ${data.browser_type} (${data.headless ? '无头' : '有头'})`;
                } else {
                    browserStatus.textContent = '⏳ 未启动';
                }
            } catch (error) {
                browserStatus.textContent = '❌ 检查失败';
                console.error('Browser check failed:', error);
            }
        }

        // 表单提交处理
        chatForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            // 添加用户消息
            addMessage('user', message);
            messageInput.value = '';
            
            // 发送到API
            await sendMessage(message);
        });

        // 添加消息到界面
        function addMessage(role, content, isLoading = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? '你' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isLoading) {
                contentDiv.innerHTML = `
                    <div class="loading">
                        <span>AI正在思考</span>
                        <div class="loading-dots">
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                            <div class="loading-dot"></div>
                        </div>
                    </div>
                `;
            } else {
                contentDiv.textContent = content;
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageDiv;
        }

        // 发送消息到API
        async function sendMessage(message) {
            isLoading = true;
            sendButton.disabled = true;
            
            // 添加加载消息
            const loadingMessage = addMessage('assistant', '', true);
            
            try {
                // 构建对话历史
                conversationHistory.push({ role: 'user', content: message });
                
                const response = await fetch(`${API_BASE}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: conversationHistory,
                        temperature: 0.7,
                        max_tokens: 2048
                    })
                });

                // 移除加载消息
                loadingMessage.remove();

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    
                    // 添加AI回复
                    addMessage('assistant', aiResponse);
                    
                    // 更新对话历史
                    conversationHistory.push({ role: 'assistant', content: aiResponse });
                    
                    // 限制历史长度
                    if (conversationHistory.length > 20) {
                        conversationHistory = conversationHistory.slice(-20);
                    }
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
            } catch (error) {
                // 移除加载消息
                loadingMessage.remove();
                
                // 显示错误
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = `❌ 发送失败: ${error.message}`;
                chatMessages.appendChild(errorDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                console.error('Send message failed:', error);
            } finally {
                isLoading = false;
                sendButton.disabled = false;
                messageInput.focus();
            }
        }

        // AI网站管理函数
        async function loadAvailableAIWebsites() {
            try {
                const response = await fetch(`${API_BASE}/ai-website/available`);
                const data = await response.json();

                if (data.websites && data.websites.length > 0) {
                    aiWebsiteSelect.innerHTML = '';
                    data.websites.forEach(website => {
                        const option = document.createElement('option');
                        option.value = website.key;
                        option.textContent = `${website.name} (${website.key})`;
                        if (website.current) {
                            option.selected = true;
                        }
                        aiWebsiteSelect.appendChild(option);
                    });
                    aiWebsitePanel.style.display = 'block';
                } else {
                    aiWebsiteSelect.innerHTML = '<option value="">无可用网站</option>';
                }
            } catch (error) {
                console.error('Load AI websites failed:', error);
                aiWebsiteSelect.innerHTML = '<option value="">加载失败</option>';
            }
        }

        async function checkAIWebsiteStatus() {
            try {
                const response = await fetch(`${API_BASE}/ai-website/status`);
                const data = await response.json();

                if (data.current_website) {
                    aiWebsiteStatus.textContent = `✅ ${data.current_website}`;
                    if (data.current_url) {
                        aiWebsiteStatus.title = data.current_url;
                    }
                } else {
                    aiWebsiteStatus.textContent = '⚠️ 未导航';
                }
            } catch (error) {
                aiWebsiteStatus.textContent = '❌ 检查失败';
                console.error('AI website status check failed:', error);
            }
        }

        async function navigateToAIWebsite() {
            const selectedWebsite = aiWebsiteSelect.value;
            if (!selectedWebsite) {
                alert('请选择一个AI网站');
                return;
            }

            navigateButton.disabled = true;
            navigateButton.textContent = '导航中...';

            try {
                const response = await fetch(`${API_BASE}/ai-website/navigate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        website: selectedWebsite,
                        auto_configure: true
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 更新状态显示
                    checkAIWebsiteStatus();

                    // 显示成功消息
                    const successDiv = document.createElement('div');
                    successDiv.style.cssText = 'background: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 4px; border: 1px solid #c3e6cb;';
                    successDiv.textContent = `✅ ${data.message}`;
                    chatMessages.appendChild(successDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    // 3秒后移除消息
                    setTimeout(() => {
                        if (successDiv.parentNode) {
                            successDiv.remove();
                        }
                    }, 3000);
                } else {
                    throw new Error(data.message || '导航失败');
                }
            } catch (error) {
                // 显示错误消息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = `❌ 导航失败: ${error.message}`;
                chatMessages.appendChild(errorDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;

                console.error('Navigate to AI website failed:', error);
            } finally {
                navigateButton.disabled = false;
                navigateButton.textContent = '导航';
            }
        }

        // 定期检查状态
        setInterval(() => {
            checkServiceStatus();
            checkAIWebsiteStatus();
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>
