"""
测试扣子网站的位置点击功能
验证复杂选择器的替代方案
"""

import sys
import os
import time
from loguru import logger

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.browser_controller.browser_manager import BrowserManager
from src.browser_controller.input_handler import InputHandler


def test_position_click_basic():
    """测试基本位置点击功能"""
    print("🎯 测试基本位置点击功能")
    print("-" * 50)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = InputHandler(browser_manager)
    
    # 创建测试页面，模拟扣子的复杂结构
    test_html = """
    <html>
    <head><title>Coze Position Click Test</title></head>
    <body style="margin:0; padding:20px; font-family: Arial;">
        <div style="height: 600px; display: flex; flex-direction: column;">
            <h1>扣子位置点击测试页面</h1>
            
            <!-- 模拟聊天区域 -->
            <div style="flex: 1; background: #f5f5f5; padding: 20px; margin: 10px 0;">
                <div>聊天消息区域...</div>
            </div>
            
            <!-- 模拟复杂的输入框结构 -->
            <div style="position: relative; bottom: 0; width: 100%; padding: 20px; background: white; border-top: 1px solid #ddd;">
                <div class="flex md:m-2 flex-1 box-content rounded md:relative md:w-auto transition-all duration-300">
                    <div>
                        <div class="rounded-xl w-full max-w-[800px] p-4 pt-0">
                            <div>
                                <div>
                                    <div>
                                        <div class="cm-scroller">
                                            <div class="cm-content cm-lineWrapping" 
                                                 contenteditable="true" 
                                                 style="min-height: 40px; padding: 10px; border: 1px solid #ccc; border-radius: 8px; background: white;"
                                                 placeholder="输入您的问题...">
                                                <!-- 这里是实际的输入区域 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // 模拟扣子的行为
            document.querySelector('.cm-content').addEventListener('click', function() {
                this.focus();
                console.log('输入框被激活');
            });
        </script>
    </body>
    </html>
    """
    
    try:
        # 加载测试页面
        browser_manager.driver.get("data:text/html," + test_html)
        time.sleep(2)
        
        # 模拟为扣子网站
        browser_manager.driver.execute_script("window.history.replaceState(null, null, 'https://space.coze.cn/task/123');")
        
        print("📋 测试场景:")
        
        # 测试场景1: 使用默认位置点击
        print("\n1. 使用扣子网站的默认位置点击")
        element = input_handler.find_input_by_position_click()
        if element:
            tag_name = element.tag_name
            class_name = element.get_attribute('class') or 'no-class'
            print(f"   ✅ 找到元素: {tag_name} (class: {class_name[:50]}...)")
            
            # 测试输入
            try:
                element.send_keys("通过位置点击输入的测试文本")
                print(f"   ✅ 输入测试成功")
            except Exception as e:
                print(f"   ❌ 输入测试失败: {e}")
        else:
            print(f"   ❌ 未找到元素")
        
        # 测试场景2: 使用自定义位置点击
        print("\n2. 使用自定义位置点击")
        window_size = browser_manager.driver.get_window_size()
        width, height = window_size["width"], window_size["height"]
        
        custom_positions = [
            {"x": width // 2, "y": height - 60, "description": "底部中心位置"},
            {"x": width // 2, "y": height - 100, "description": "底部偏上位置"},
            {"x": 400, "y": height - 80, "description": "固定位置400px"},
        ]
        
        element = input_handler.find_input_by_position_click(custom_positions)
        if element:
            tag_name = element.tag_name
            class_name = element.get_attribute('class') or 'no-class'
            print(f"   ✅ 找到元素: {tag_name} (class: {class_name[:50]}...)")
        else:
            print(f"   ❌ 未找到元素")
        
        # 测试场景3: 智能查找 + 位置点击回退
        print("\n3. 智能查找 + 位置点击回退")
        
        # 先尝试复杂选择器（应该失败）
        complex_selector = "#root > div > div.flex.md\\:m-2.flex-1.box-content.rounded.md\\:relative.md\\:w-auto.transition-all.duration-300.ease-\\[cubic-bezier\\(0\\.65\\,0\\,0\\.35\\,0\\)\\] > div > div.rounded-xl.w-full.max-w-\\[800px\\].p-4.pt-0 > div > div:nth-child(1) > div > div.cm-scroller > div.cm-content.cm-lineWrapping"
        
        element = input_handler.find_input_element_smart(complex_selector, use_position_click=True)
        if element:
            tag_name = element.tag_name
            class_name = element.get_attribute('class') or 'no-class'
            print(f"   ✅ 智能查找成功: {tag_name} (class: {class_name[:50]}...)")
        else:
            print(f"   ❌ 智能查找失败")
        
        # 测试场景4: 完整的输入流程
        print("\n4. 完整的输入流程测试")
        success = input_handler.input_text(
            "这是通过位置点击输入的完整测试消息", 
            selector=None,  # 不指定选择器
            use_smart_search=True, 
            use_position_click=True
        )
        
        if success:
            print(f"   ✅ 完整输入流程成功")
        else:
            print(f"   ❌ 完整输入流程失败")
        
        print("\n✅ 位置点击功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 关闭浏览器
        browser_manager.driver.quit()


def test_real_coze_website():
    """测试真实的扣子网站（如果可访问）"""
    print("\n🌐 测试真实扣子网站")
    print("-" * 50)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = InputHandler(browser_manager)
    
    try:
        # 尝试访问扣子网站
        test_url = "https://www.coze.cn"  # 使用主页，避免需要登录
        print(f"📍 尝试访问: {test_url}")
        
        browser_manager.driver.get(test_url)
        time.sleep(3)  # 等待页面加载
        
        current_url = browser_manager.driver.current_url
        print(f"   实际URL: {current_url}")
        
        # 获取扣子网站的点击位置配置
        config = input_handler.get_website_config()
        print(f"   网站识别: {config['name']}")
        
        # 获取推荐点击位置
        positions = input_handler._get_website_click_positions()
        print(f"   推荐点击位置数量: {len(positions)}")
        
        for i, pos in enumerate(positions[:3]):
            print(f"     {i+1}. {pos['description']}: ({pos['x']}, {pos['y']})")
        
        # 尝试位置点击（如果页面有输入框）
        print("\n🎯 尝试位置点击查找输入框")
        element = input_handler.find_input_by_position_click()
        
        if element:
            tag_name = element.tag_name
            class_name = element.get_attribute('class') or 'no-class'
            print(f"   ✅ 找到输入元素: {tag_name}")
            print(f"   📝 元素类名: {class_name[:100]}...")
        else:
            print(f"   ℹ️ 未找到输入框（可能需要登录或导航到聊天页面）")
        
        print("\n✅ 真实网站测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 真实网站测试失败: {e}")
        return False
    finally:
        # 关闭浏览器
        browser_manager.driver.quit()


def main():
    """主函数"""
    print("🚀 扣子网站位置点击功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试基本位置点击功能
        if test_position_click_basic():
            print("✅ 基本位置点击功能测试通过")
        else:
            print("❌ 基本位置点击功能测试失败")
        
        # 2. 测试真实扣子网站
        if test_real_coze_website():
            print("✅ 真实网站测试通过")
        else:
            print("❌ 真实网站测试失败")
        
        print("\n" + "=" * 60)
        print("🎉 扣子位置点击功能测试完成！")
        
        print("\n💡 功能总结:")
        print("✅ 位置点击激活 - 通过点击页面位置激活输入框")
        print("✅ 网站特定位置 - 针对扣子网站优化的点击位置")
        print("✅ 智能回退机制 - 选择器失败时自动使用位置点击")
        print("✅ 自定义位置支持 - 支持指定自定义点击位置")
        print("✅ 激活元素获取 - 获取点击后激活的输入元素")
        
        print("\n🔧 解决的问题:")
        print("• 复杂CSS选择器容易变化的问题")
        print("• 动态生成元素难以定位的问题")
        print("• 不同页面状态下输入框位置变化的问题")
        print("• 提供了选择器的可靠替代方案")
        
        print("\n📍 扣子网站特定优化:")
        print("• 7个优化的点击位置")
        print("• 覆盖底部输入区域的不同位置")
        print("• 支持不同屏幕尺寸的自适应")
        print("• 提供固定位置作为备选方案")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
