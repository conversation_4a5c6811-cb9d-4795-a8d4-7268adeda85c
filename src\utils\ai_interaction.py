"""
AI交互管理器 - 统一管理AI询问时的浏览器交互逻辑
整合输入处理和响应监听，提供高级的AI交互功能
"""

import time
import asyncio
from typing import Optional, Dict, Any, List, Callable
from loguru import logger

from ..browser_controller import <PERSON><PERSON>er<PERSON>ana<PERSON>, InputHandler, ResponseMonitor


class AIInteractionManager:
    """AI交互管理器类"""

    def __init__(self, browser_manager: BrowserManager):
        """
        初始化AI交互管理器
        参数: browser_manager - 浏览器管理器实例

        使用思路：
        1. 整合浏览器控制的各个组件
        2. 提供统一的AI交互接口
        3. 管理交互状态和错误处理
        """
        self.browser_manager = browser_manager
        self.input_handler = InputHandler(browser_manager)
        self.response_monitor = ResponseMonitor(browser_manager)

        # 交互状态
        self.is_interacting = False
        self.last_interaction_time = None
        self.interaction_history: List[Dict[str, Any]] = []

        # 配置参数
        self.default_timeout = 60  # 默认响应超时时间(秒)
        self.retry_count = 3  # 重试次数
        self.retry_delay = 2  # 重试延迟(秒)

        logger.info("AI交互管理器初始化完成")
    
    async def ask_ai(self, question: str, 
                     input_selector: Optional[str] = None,
                     response_selector: Optional[str] = None,
                     timeout: Optional[int] = None) -> Optional[str]:
        """
        向AI提问并获取回复
        
        参数:
            question - 要提问的问题
            input_selector - 输入框选择器，为空则自动查找
            response_selector - 响应内容选择器，为空则自动查找
            timeout - 响应超时时间，为空则使用默认值
        返回: AI的回复内容或None
        
        使用思路：
        1. 这是核心的AI交互方法
        2. 自动处理输入、提交和响应监听
        3. 包含错误处理和重试机制
        
        使用例子：
        manager = AIInteractionManager(browser_manager)
        response = await manager.ask_ai("什么是人工智能？")
        print(f"AI回复: {response}")
        """
        if self.is_interacting:
            logger.warning("正在进行其他AI交互，请稍后再试")
            return None
        
        self.is_interacting = True
        timeout = timeout or self.default_timeout
        
        try:
            logger.info(f"开始AI交互，问题: {question[:100]}...")
            
            # 记录交互开始
            interaction_record = {
                "question": question,
                "start_time": time.time(),
                "status": "started"
            }
            
            # 检查浏览器状态
            if not self.browser_manager.is_browser_running():
                raise Exception("浏览器未运行")
            
            # 等待输入框准备就绪
            if not self.input_handler.wait_for_input_ready(input_selector, timeout=10):
                raise Exception("输入框未准备就绪")
            
            # 输入问题并提交
            success = self.input_handler.input_and_submit(question, input_selector)
            if not success:
                raise Exception("输入问题失败")
            
            logger.info("问题已提交，等待AI响应...")
            
            # 等待AI响应
            ai_response = self.response_monitor.wait_for_response(timeout, response_selector)
            
            if ai_response:
                # 记录成功的交互
                interaction_record.update({
                    "response": ai_response,
                    "end_time": time.time(),
                    "status": "success",
                    "duration": time.time() - interaction_record["start_time"]
                })
                
                self.interaction_history.append(interaction_record)
                self.last_interaction_time = time.time()
                
                logger.info(f"AI交互成功，响应长度: {len(ai_response)} 字符")
                return ai_response
            else:
                raise Exception("未收到AI响应或响应超时")
                
        except Exception as e:
            # 记录失败的交互
            interaction_record.update({
                "error": str(e),
                "end_time": time.time(),
                "status": "failed",
                "duration": time.time() - interaction_record["start_time"]
            })
            
            self.interaction_history.append(interaction_record)
            
            logger.error(f"AI交互失败: {e}")
            return None
            
        finally:
            self.is_interacting = False
    
    async def ask_ai_with_retry(self, question: str,
                               input_selector: Optional[str] = None,
                               response_selector: Optional[str] = None,
                               timeout: Optional[int] = None,
                               max_retries: Optional[int] = None) -> Optional[str]:
        """
        带重试机制的AI提问
        
        参数:
            question - 要提问的问题
            input_selector - 输入框选择器
            response_selector - 响应内容选择器
            timeout - 响应超时时间
            max_retries - 最大重试次数
        返回: AI的回复内容或None
        
        使用思路：
        1. 在网络不稳定或页面加载慢时使用
        2. 自动重试失败的交互
        3. 提高交互成功率
        
        使用例子：
        # 最多重试3次
        response = await manager.ask_ai_with_retry("复杂问题", max_retries=3)
        """
        max_retries = max_retries or self.retry_count
        
        for attempt in range(max_retries + 1):
            try:
                response = await self.ask_ai(question, input_selector, response_selector, timeout)
                
                if response:
                    if attempt > 0:
                        logger.info(f"重试成功，尝试次数: {attempt + 1}")
                    return response
                
                if attempt < max_retries:
                    logger.warning(f"第 {attempt + 1} 次尝试失败，{self.retry_delay}秒后重试...")
                    await asyncio.sleep(self.retry_delay)
                
            except Exception as e:
                if attempt < max_retries:
                    logger.warning(f"第 {attempt + 1} 次尝试异常: {e}，{self.retry_delay}秒后重试...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    logger.error(f"所有重试尝试都失败了: {e}")
        
        logger.error(f"经过 {max_retries + 1} 次尝试后仍然失败")
        return None
    
    def get_interaction_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取交互历史记录
        
        参数: limit - 返回记录数量限制
        返回: 交互历史记录列表
        
        使用思路：
        1. 用于调试和分析交互模式
        2. 监控AI交互的成功率
        3. 优化交互策略
        
        使用例子：
        # 获取最近10次交互记录
        history = manager.get_interaction_history(10)
        for record in history:
            print(f"问题: {record['question'][:50]}...")
            print(f"状态: {record['status']}")
        """
        if limit:
            return self.interaction_history[-limit:]
        return self.interaction_history.copy()
    
    def get_interaction_stats(self) -> Dict[str, Any]:
        """
        获取交互统计信息
        
        返回: 统计信息字典
        
        使用思路：
        1. 分析AI交互的性能指标
        2. 监控系统健康状态
        3. 优化配置参数
        
        使用例子：
        stats = manager.get_interaction_stats()
        print(f"总交互次数: {stats['total_interactions']}")
        print(f"成功率: {stats['success_rate']:.2%}")
        print(f"平均响应时间: {stats['avg_duration']:.2f}秒")
        """
        if not self.interaction_history:
            return {
                "total_interactions": 0,
                "successful_interactions": 0,
                "failed_interactions": 0,
                "success_rate": 0.0,
                "avg_duration": 0.0,
                "last_interaction_time": None
            }
        
        total = len(self.interaction_history)
        successful = len([r for r in self.interaction_history if r["status"] == "success"])
        failed = total - successful
        
        # 计算平均持续时间
        durations = [r.get("duration", 0) for r in self.interaction_history if "duration" in r]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        return {
            "total_interactions": total,
            "successful_interactions": successful,
            "failed_interactions": failed,
            "success_rate": successful / total if total > 0 else 0.0,
            "avg_duration": avg_duration,
            "last_interaction_time": self.last_interaction_time
        }
    
    def clear_history(self):
        """
        清空交互历史记录
        
        使用思路：
        1. 定期清理历史记录以节省内存
        2. 重置统计信息
        
        使用例子：
        manager.clear_history()
        """
        self.interaction_history.clear()
        self.last_interaction_time = None
        logger.info("交互历史记录已清空")
    
    def is_ready_for_interaction(self) -> bool:
        """
        检查是否准备好进行AI交互
        
        返回: 是否准备就绪
        
        使用思路：
        1. 在发起交互前检查系统状态
        2. 确保浏览器和组件正常工作
        
        使用例子：
        if manager.is_ready_for_interaction():
            response = await manager.ask_ai("问题")
        else:
            print("系统未准备就绪")
        """
        try:
            # 检查是否正在交互
            if self.is_interacting:
                return False
            
            # 检查浏览器状态
            if not self.browser_manager.is_browser_running():
                return False
            
            # 检查输入框是否可用（快速检查）
            input_element = self.input_handler.find_input_element()
            if not input_element:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查交互准备状态失败: {e}")
            return False
