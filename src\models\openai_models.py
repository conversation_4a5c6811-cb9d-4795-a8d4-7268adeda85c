"""
OpenAI API兼容的数据模型
定义与OpenAI Chat Completions API兼容的请求和响应结构
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
import time


class ChatMessage(BaseModel):
    """
    聊天消息模型
    
    使用思路：
    1. 表示对话中的单条消息
    2. role可以是'system', 'user', 'assistant'
    3. content包含消息的实际文本内容
    
    使用例子：
    user_msg = ChatMessage(role="user", content="你好")
    assistant_msg = ChatMessage(role="assistant", content="你好！有什么可以帮助你的吗？")
    """
    role: str = Field(..., description="消息角色：system/user/assistant")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")


class ChatCompletionRequest(BaseModel):
    """
    聊天完成请求模型
    
    使用思路：
    1. 接收客户端的聊天请求
    2. 包含对话历史和生成参数
    3. 兼容OpenAI API格式
    
    使用例子：
    request = ChatCompletionRequest(
        model="gpt-3.5-turbo",
        messages=[
            ChatMessage(role="user", content="什么是人工智能？")
        ],
        temperature=0.7,
        max_tokens=1000
    )
    """
    model: str = Field(default="gpt-3.5-turbo", description="模型名称")
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    temperature: Optional[float] = Field(default=0.7, ge=0, le=2, description="温度参数，控制随机性")
    max_tokens: Optional[int] = Field(default=2048, gt=0, description="最大生成token数")
    top_p: Optional[float] = Field(default=1.0, ge=0, le=1, description="核采样参数")
    frequency_penalty: Optional[float] = Field(default=0, ge=-2, le=2, description="频率惩罚")
    presence_penalty: Optional[float] = Field(default=0, ge=-2, le=2, description="存在惩罚")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止序列")
    stream: Optional[bool] = Field(default=False, description="是否流式返回")
    user: Optional[str] = Field(None, description="用户标识")


class Usage(BaseModel):
    """
    使用量统计模型
    
    使用思路：
    1. 统计API调用的token使用情况
    2. 兼容OpenAI API的使用量格式
    
    使用例子：
    usage = Usage(
        prompt_tokens=50,
        completion_tokens=100,
        total_tokens=150
    )
    """
    prompt_tokens: int = Field(..., description="输入token数量")
    completion_tokens: int = Field(..., description="生成token数量")
    total_tokens: int = Field(..., description="总token数量")


class ChatCompletionChoice(BaseModel):
    """
    聊天完成选择模型
    
    使用思路：
    1. 表示AI生成的一个回复选项
    2. 包含消息内容和结束原因
    
    使用例子：
    choice = ChatCompletionChoice(
        index=0,
        message=ChatMessage(role="assistant", content="这是AI的回复"),
        finish_reason="stop"
    )
    """
    index: int = Field(..., description="选择索引")
    message: ChatMessage = Field(..., description="回复消息")
    finish_reason: Optional[str] = Field(None, description="结束原因：stop/length/content_filter")


class ChatCompletionResponse(BaseModel):
    """
    聊天完成响应模型
    
    使用思路：
    1. 返回给客户端的完整响应
    2. 包含生成的回复和元数据
    3. 完全兼容OpenAI API格式
    
    使用例子：
    response = ChatCompletionResponse(
        id="chatcmpl-123",
        object="chat.completion",
        created=int(time.time()),
        model="gpt-3.5-turbo",
        choices=[choice],
        usage=usage
    )
    """
    id: str = Field(..., description="响应ID")
    object: str = Field(default="chat.completion", description="对象类型")
    created: int = Field(default_factory=lambda: int(time.time()), description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    choices: List[ChatCompletionChoice] = Field(..., description="回复选择列表")
    usage: Optional[Usage] = Field(None, description="使用量统计")


class ErrorResponse(BaseModel):
    """
    错误响应模型
    
    使用思路：
    1. 统一的错误响应格式
    2. 提供详细的错误信息
    
    使用例子：
    error = ErrorResponse(
        error={
            "message": "浏览器未启动",
            "type": "browser_error",
            "code": "browser_not_started"
        }
    )
    """
    error: Dict[str, Any] = Field(..., description="错误详情")


class BrowserStatus(BaseModel):
    """
    浏览器状态模型
    
    使用思路：
    1. 表示当前浏览器的运行状态
    2. 用于健康检查和状态监控
    
    使用例子：
    status = BrowserStatus(
        running=True,
        current_url="https://example.com",
        browser_type="chrome"
    )
    """
    running: bool = Field(..., description="浏览器是否运行中")
    current_url: Optional[str] = Field(None, description="当前页面URL")
    browser_type: str = Field(..., description="浏览器类型")
    headless: bool = Field(..., description="是否无头模式")
    last_activity: Optional[int] = Field(None, description="最后活动时间戳")
