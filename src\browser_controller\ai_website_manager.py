"""
AI网站管理器 - 负责导航到AI网站并配置相应的选择器
支持ChatGPT、<PERSON>、Bard等主流AI对话网站
"""

import time
from typing import Optional, List, Dict, Any, Tuple
from loguru import logger

from .browser_manager import BrowserManager
from .input_handler import InputHandler
from .response_monitor import ResponseMonitor
from config import settings


class AIWebsiteManager:
    """AI网站管理器类"""
    
    def __init__(self, browser_manager: BrowserManager):
        """
        初始化AI网站管理器
        参数: browser_manager - 浏览器管理器实例
        
        使用思路：
        1. 管理不同AI网站的配置
        2. 自动导航到指定AI网站
        3. 动态配置输入和响应选择器
        """
        self.browser_manager = browser_manager
        self.config = settings.ai_website
        self.current_website = None
        self.current_url = None
        
        # AI网站配置映射
        self.website_configs = {
            "chatgpt": {
                "url": self.config.chatgpt_url,
                "name": "Chat<PERSON><PERSON>",
                "input_selectors": self.config.chatgpt_input_selectors,
                "response_selectors": self.config.chatgpt_response_selectors,
                "wait_time": 8  # ChatGPT需要更长的加载时间
            },
            "claude": {
                "url": self.config.claude_url,
                "name": "Claude",
                "input_selectors": self.config.claude_input_selectors,
                "response_selectors": self.config.claude_response_selectors,
                "wait_time": 6
            },
            "bard": {
                "url": self.config.bard_url,
                "name": "Google Bard",
                "input_selectors": self.config.bard_input_selectors,
                "response_selectors": self.config.bard_response_selectors,
                "wait_time": 5
            },
            "custom": {
                "url": self.config.custom_ai_url,
                "name": "自定义AI网站",
                "input_selectors": settings.input.input_selectors,
                "response_selectors": settings.response.response_selectors,
                "wait_time": self.config.page_load_wait_time
            }
        }
    
    def navigate_to_ai_website(self, website: Optional[str] = None) -> bool:
        """
        导航到指定的AI网站
        
        参数: website - AI网站名称 (chatgpt/claude/bard/custom)，为空则使用默认配置
        返回: 导航是否成功
        
        使用思路：
        1. 根据配置选择AI网站
        2. 导航到对应URL
        3. 等待页面加载完成
        4. 更新当前网站状态
        
        使用例子：
        manager = AIWebsiteManager(browser_manager)
        success = manager.navigate_to_ai_website("chatgpt")
        if success:
            print("成功导航到ChatGPT")
        """
        try:
            if not self.browser_manager.is_browser_running():
                logger.error("浏览器未运行，无法导航")
                return False
            
            # 确定要导航的网站
            target_website = website or self.config.default_ai_website
            
            if target_website not in self.website_configs:
                logger.error(f"不支持的AI网站: {target_website}")
                return False
            
            website_config = self.website_configs[target_website]
            target_url = website_config["url"]
            
            if not target_url:
                logger.error(f"{website_config['name']} 的URL未配置")
                return False
            
            logger.info(f"正在导航到 {website_config['name']}: {target_url}")
            
            # 导航到AI网站
            if not self.browser_manager.navigate_to(target_url):
                logger.error(f"导航到 {website_config['name']} 失败")
                return False
            
            # 等待页面加载
            wait_time = website_config.get("wait_time", self.config.page_load_wait_time)
            logger.info(f"等待页面加载完成 ({wait_time}秒)...")
            time.sleep(wait_time)
            
            # 更新当前状态
            self.current_website = target_website
            self.current_url = target_url
            
            logger.info(f"✅ 成功导航到 {website_config['name']}")
            
            # 验证页面是否正确加载
            if self._verify_page_loaded(target_website):
                logger.info(f"✅ {website_config['name']} 页面验证成功")
                return True
            else:
                logger.warning(f"⚠️ {website_config['name']} 页面验证失败，但导航已完成")
                return True  # 即使验证失败也认为导航成功
                
        except Exception as e:
            logger.error(f"导航到AI网站时发生错误: {e}")
            return False
    
    def _verify_page_loaded(self, website: str) -> bool:
        """
        验证AI网站页面是否正确加载
        
        参数: website - AI网站名称
        返回: 页面是否正确加载
        
        使用思路：
        1. 检查页面标题
        2. 查找关键元素
        3. 验证页面完整性
        """
        try:
            if not self.browser_manager.driver:
                return False
            
            # 获取页面标题
            page_title = self.browser_manager.driver.title.lower()
            logger.debug(f"页面标题: {page_title}")
            
            # 根据不同网站进行验证
            if website == "chatgpt":
                return "chatgpt" in page_title or "openai" in page_title
            elif website == "claude":
                return "claude" in page_title or "anthropic" in page_title
            elif website == "bard":
                return "bard" in page_title or "google" in page_title
            else:
                # 对于自定义网站，只检查是否有标题
                return len(page_title) > 0
                
        except Exception as e:
            logger.debug(f"页面验证时发生错误: {e}")
            return False
    
    def get_current_website_config(self) -> Optional[Dict[str, Any]]:
        """
        获取当前AI网站的配置
        
        返回: 当前网站配置字典或None
        
        使用思路：
        1. 返回当前网站的选择器配置
        2. 供输入处理器和响应监听器使用
        
        使用例子：
        config = manager.get_current_website_config()
        if config:
            input_selectors = config["input_selectors"]
            response_selectors = config["response_selectors"]
        """
        if self.current_website and self.current_website in self.website_configs:
            return self.website_configs[self.current_website]
        return None
    
    def update_input_handler_selectors(self, input_handler: InputHandler) -> bool:
        """
        更新输入处理器的选择器配置
        
        参数: input_handler - 输入处理器实例
        返回: 更新是否成功
        
        使用思路：
        1. 根据当前AI网站更新输入选择器
        2. 提高输入框识别的准确性
        
        使用例子：
        if manager.update_input_handler_selectors(input_handler):
            print("输入选择器已更新")
        """
        try:
            config = self.get_current_website_config()
            if config and "input_selectors" in config:
                input_handler.common_input_selectors = config["input_selectors"]
                logger.info(f"已更新输入选择器为 {self.current_website} 配置")
                return True
            return False
        except Exception as e:
            logger.error(f"更新输入选择器失败: {e}")
            return False
    
    def update_response_monitor_selectors(self, response_monitor: ResponseMonitor) -> bool:
        """
        更新响应监听器的选择器配置
        
        参数: response_monitor - 响应监听器实例
        返回: 更新是否成功
        
        使用思路：
        1. 根据当前AI网站更新响应选择器
        2. 提高AI回复识别的准确性
        
        使用例子：
        if manager.update_response_monitor_selectors(response_monitor):
            print("响应选择器已更新")
        """
        try:
            config = self.get_current_website_config()
            if config and "response_selectors" in config:
                response_monitor.response_selectors = config["response_selectors"]
                logger.info(f"已更新响应选择器为 {self.current_website} 配置")
                return True
            return False
        except Exception as e:
            logger.error(f"更新响应选择器失败: {e}")
            return False
    
    def get_available_websites(self) -> List[Dict[str, str]]:
        """
        获取可用的AI网站列表
        
        返回: AI网站信息列表
        
        使用思路：
        1. 返回所有配置的AI网站
        2. 供用户选择和API调用
        
        使用例子：
        websites = manager.get_available_websites()
        for site in websites:
            print(f"{site['key']}: {site['name']} - {site['url']}")
        """
        websites = []
        for key, config in self.website_configs.items():
            if config["url"]:  # 只返回有URL配置的网站
                websites.append({
                    "key": key,
                    "name": config["name"],
                    "url": config["url"],
                    "current": key == self.current_website
                })
        return websites
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取AI网站管理器状态
        
        返回: 状态信息字典
        
        使用思路：
        1. 返回当前网站状态
        2. 用于监控和调试
        
        使用例子：
        status = manager.get_status()
        print(f"当前网站: {status['current_website']}")
        """
        return {
            "current_website": self.current_website,
            "current_url": self.current_url,
            "default_website": self.config.default_ai_website,
            "auto_navigate": self.config.auto_navigate_on_startup,
            "available_websites": self.get_available_websites()
        }
