"""
输入处理器 - 负责处理用户输入和浏览器交互
当AI有询问时，控制浏览器进行输入和回车操作
"""

import time
from typing import Optional, List, Dict, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from loguru import logger

from .browser_manager import BrowserManager
from config import settings


class InputHandler:
    """输入处理器类"""
    
    def __init__(self, browser_manager: BrowserManager):
        """
        初始化输入处理器
        参数: browser_manager - 浏览器管理器实例
        """
        self.browser_manager = browser_manager
        self.config = settings.input
        self.common_input_selectors = self.config.input_selectors
        
    def _detect_selector_type(self, selector: str) -> str:
        """
        检测选择器类型

        参数: selector - 选择器字符串
        返回: 选择器类型 ('css', 'xpath', 'id', 'name', 'class')

        使用思路：
        1. 根据选择器语法特征判断类型
        2. 支持CSS选择器、XPath、ID、Name、Class等
        3. 提供智能识别，减少用户配置负担
        """
        if not selector:
            return 'css'

        # XPath选择器特征
        if selector.startswith('//') or selector.startswith('./') or selector.startswith('/'):
            return 'xpath'

        # ID选择器特征
        if selector.startswith('#') and ' ' not in selector and '.' not in selector[1:]:
            return 'id'

        # Class选择器特征
        if selector.startswith('.') and ' ' not in selector and '#' not in selector:
            return 'class'

        # Name属性特征
        if selector.startswith('name='):
            return 'name'

        # 默认为CSS选择器
        return 'css'

    def _find_elements_by_selector(self, selector: str) -> List[Any]:
        """
        根据选择器类型查找元素

        参数: selector - 选择器字符串
        返回: 找到的元素列表

        使用思路：
        1. 自动识别选择器类型
        2. 使用对应的查找方法
        3. 统一处理不同类型的选择器
        """
        if not self.browser_manager.driver:
            return []

        selector_type = self._detect_selector_type(selector)

        try:
            if selector_type == 'xpath':
                return self.browser_manager.driver.find_elements(By.XPATH, selector)
            elif selector_type == 'id':
                # 移除#前缀
                id_value = selector[1:] if selector.startswith('#') else selector
                return self.browser_manager.driver.find_elements(By.ID, id_value)
            elif selector_type == 'class':
                # 移除.前缀
                class_value = selector[1:] if selector.startswith('.') else selector
                return self.browser_manager.driver.find_elements(By.CLASS_NAME, class_value)
            elif selector_type == 'name':
                # 移除name=前缀
                name_value = selector[5:] if selector.startswith('name=') else selector
                return self.browser_manager.driver.find_elements(By.NAME, name_value)
            else:  # CSS选择器
                return self.browser_manager.driver.find_elements(By.CSS_SELECTOR, selector)

        except Exception as e:
            logger.debug(f"选择器 {selector} ({selector_type}) 查找失败: {e}")
            return []

    def find_input_element(self, custom_selector: Optional[str] = None) -> Optional[Any]:
        """
        查找页面中的输入框元素（支持CSS选择器和XPath）

        参数: custom_selector - 自定义选择器，支持CSS、XPath、ID、Class等
        返回: 找到的输入元素或None

        使用思路：
        1. 如果提供了自定义选择器，优先使用（支持多种选择器类型）
        2. 否则按照当前网站的输入框选择器优先级依次尝试
        3. 返回第一个找到的可见且可交互的输入框
        4. 自动识别选择器类型（CSS、XPath、ID等）

        使用例子：
        handler = InputHandler(browser_manager)
        # 使用CSS选择器
        input_elem = handler.find_input_element('#chat-input')
        # 使用XPath选择器
        input_elem = handler.find_input_element('//textarea[@placeholder="输入消息"]')
        # 使用ID选择器
        input_elem = handler.find_input_element('#message-input')
        # 使用Class选择器
        input_elem = handler.find_input_element('.chat-input')
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return None

            # 确定要使用的选择器列表
            if custom_selector:
                selectors = [custom_selector]
                logger.debug(f"使用自定义选择器: {custom_selector}")
            else:
                selectors = self.common_input_selectors
                logger.debug(f"使用默认选择器列表，共 {len(selectors)} 个")

            # 遍历选择器列表
            for selector in selectors:
                if not selector:
                    continue

                try:
                    # 使用智能选择器查找
                    elements = self._find_elements_by_selector(selector)

                    for element in elements:
                        try:
                            # 检查元素是否可见且可交互
                            if element.is_displayed() and element.is_enabled():
                                # 额外检查是否为输入类型的元素
                                tag_name = element.tag_name.lower()
                                if tag_name in ['input', 'textarea'] or element.get_attribute('contenteditable') == 'true':
                                    selector_type = self._detect_selector_type(selector)
                                    logger.info(f"找到输入框: {selector} (类型: {selector_type})")
                                    return element
                        except Exception as e:
                            logger.debug(f"检查元素可用性失败: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"选择器 {selector} 查找失败: {e}")
                    continue

            logger.warning("未找到可用的输入框")
            logger.debug(f"尝试了 {len(selectors)} 个选择器: {selectors}")
            return None

        except Exception as e:
            logger.error(f"查找输入框时发生错误: {e}")
            return None
    
    def input_text(self, text: str, selector: Optional[str] = None, clear_first: bool = True,
                   use_website_specific: bool = True, use_smart_search: bool = True,
                   use_position_click: bool = True) -> bool:
        """
        在输入框中输入文本（支持智能选择器查找和网站特定优化）

        参数:
            text - 要输入的文本内容
            selector - 输入框选择器（支持CSS、XPath、ID等），为空则自动查找
            clear_first - 是否先清空输入框，默认为True
            use_website_specific - 是否使用网站特定的选择器优化
            use_smart_search - 是否使用智能搜索（优先指定选择器，失败时自动回退）
        返回: 输入是否成功

        使用思路：
        1. 优先使用指定的选择器，如果找不到则自动回退到网站特定配置
        2. 支持多种选择器语法（CSS、XPath、ID等）
        3. 根据网站类型智能选择最佳选择器
        4. 查找输入框元素并验证可用性
        5. 聚焦到输入框并输入文本

        使用例子：
        # 基本使用（智能搜索 + 网站特定优化）
        success = handler.input_text("Hello World")

        # 指定选择器，失败时自动回退
        success = handler.input_text("Hello", "#chat-input", clear_first=False)

        # 使用XPath选择器，失败时自动回退
        success = handler.input_text("Hello", '//textarea[@placeholder="输入消息"]')

        # 禁用智能搜索，只使用指定选择器
        success = handler.input_text("Hello", "#chat-input", use_smart_search=False)

        # 禁用网站特定优化
        success = handler.input_text("Hello", use_website_specific=False)
        """
        try:
            # 选择查找方法
            if use_smart_search:
                # 使用智能查找（优先指定选择器，失败时自动回退）
                input_element = self.find_input_element_smart(selector, use_website_specific, use_position_click)
            else:
                # 使用传统查找方法
                original_selectors = None
                if not selector and use_website_specific:
                    original_selectors = self.common_input_selectors
                    self.common_input_selectors = self.get_website_specific_selectors()
                    logger.debug("使用网站特定选择器进行输入")

                input_element = self.find_input_element(selector)

                # 恢复原始选择器
                if original_selectors is not None:
                    self.common_input_selectors = original_selectors

            if not input_element:
                logger.warning("未找到可用的输入框")
                return False
            
            # 聚焦到输入框
            input_element.click()
            time.sleep(0.1)  # 短暂等待确保聚焦

            # 清空输入框（如果需要）
            clear_first = clear_first if clear_first is not None else self.config.clear_before_input
            if clear_first:
                input_element.clear()
                # 对于某些特殊输入框，使用Ctrl+A + Delete
                input_element.send_keys(Keys.CONTROL + "a")
                input_element.send_keys(Keys.DELETE)

            # 输入文本（带延迟）
            if self.config.input_delay > 0:
                for char in text:
                    input_element.send_keys(char)
                    time.sleep(self.config.input_delay)
            else:
                input_element.send_keys(text)
            
            # 验证输入结果
            current_value = input_element.get_attribute("value") or input_element.text
            if text in current_value:
                logger.info(f"文本输入成功: {text[:50]}...")
                return True
            else:
                logger.warning(f"文本输入可能不完整，期望包含: {text[:50]}..., 实际: {current_value[:50]}...")
                return True  # 某些情况下验证可能不准确，但输入操作已执行

        except Exception as e:
            logger.error(f"输入文本失败: {e}")
            return False

    def press_enter(self, selector: Optional[str] = None, use_website_specific: bool = True,
                   use_smart_search: bool = True) -> bool:
        """
        在输入框中按回车键（支持多种选择器类型和网站特定优化）

        参数:
            selector - 输入框选择器（支持CSS、XPath、ID等），为空则自动查找
            use_website_specific - 是否使用网站特定的选择器优化
        返回: 操作是否成功

        使用思路：
        1. 根据网站类型智能选择最佳选择器
        2. 查找输入框元素并验证可用性
        3. 确保输入框处于聚焦状态
        4. 发送回车键

        使用例子：
        # 在当前聚焦的输入框按回车（使用网站特定优化）
        success = handler.press_enter()

        # 使用CSS选择器
        success = handler.press_enter("#search-box")

        # 使用XPath选择器
        success = handler.press_enter('//textarea[@placeholder="输入消息"]')
        """
        try:
            # 选择查找方法
            if use_smart_search:
                # 使用智能查找
                input_element = self.find_input_element_smart(selector, use_website_specific)
            else:
                # 使用传统查找方法
                original_selectors = None
                if not selector and use_website_specific:
                    original_selectors = self.common_input_selectors
                    self.common_input_selectors = self.get_website_specific_selectors()
                    logger.debug("使用网站特定选择器按回车")

                input_element = self.find_input_element(selector)

                # 恢复原始选择器
                if original_selectors is not None:
                    self.common_input_selectors = original_selectors

            if not input_element:
                logger.warning("未找到可用的输入框")
                return False

            # 确保输入框聚焦
            input_element.click()
            time.sleep(0.1)

            # 按回车键
            input_element.send_keys(Keys.RETURN)

            logger.info("回车键发送成功")
            return True

        except Exception as e:
            logger.error(f"发送回车键失败: {e}")
            return False

    def input_and_submit(self, text: str, selector: Optional[str] = None, clear_first: bool = True,
                        use_website_specific: bool = True, use_smart_search: bool = True) -> bool:
        """
        输入文本并按回车提交（支持多种选择器类型和网站特定优化）

        参数:
            text - 要输入的文本
            selector - 输入框选择器（支持CSS、XPath、ID等）
            clear_first - 是否先清空输入框
            use_website_specific - 是否使用网站特定的选择器优化
        返回: 操作是否成功

        使用思路：
        1. 根据网站类型智能选择最佳选择器
        2. 先输入文本
        3. 然后按回车提交
        4. 这是AI询问时的常用操作组合

        使用例子：
        # 输入问题并提交（使用网站特定优化）
        success = handler.input_and_submit("什么是人工智能？")

        # 使用CSS选择器
        success = handler.input_and_submit("Hello", "#chat-input")

        # 使用XPath选择器
        success = handler.input_and_submit("Hello", '//textarea[@placeholder="输入消息"]')
        """
        try:
            # 先输入文本
            if not self.input_text(text, selector, clear_first, use_website_specific, use_smart_search):
                return False

            # 短暂等待确保输入完成
            time.sleep(0.2)

            # 按回车提交
            return self.press_enter(selector, use_website_specific, use_smart_search)

        except Exception as e:
            logger.error(f"输入并提交失败: {e}")
            return False

    def get_website_config(self) -> Dict[str, Any]:
        """
        获取当前网站的完整配置信息

        返回: 包含选择器、类型、文本等信息的配置字典

        使用思路：
        1. 检测当前网站URL
        2. 返回该网站的完整配置（选择器、类型、占位符文本等）
        3. 支持优先级排序和自动回退
        4. 提供更精确的网站适配

        配置结构：
        {
            "name": "网站名称",
            "selectors": [
                {"selector": "选择器", "type": "类型", "priority": 优先级, "description": "描述"},
                ...
            ],
            "placeholder_texts": ["占位符文本1", "占位符文本2", ...],
            "submit_methods": ["提交方式1", "提交方式2", ...],
            "wait_time": 等待时间
        }
        """
        try:
            if not self.browser_manager.driver:
                return self._get_default_config()

            current_url = self.browser_manager.driver.current_url.lower()

            # ChatGPT / OpenAI
            if 'chat.openai.com' in current_url or 'openai.com' in current_url:
                return {
                    "name": "ChatGPT",
                    "selectors": [
                        {"selector": 'textarea[data-id="root"]', "type": "css", "priority": 1, "description": "主输入框"},
                        {"selector": '//textarea[@data-id="root"]', "type": "xpath", "priority": 2, "description": "主输入框XPath"},
                        {"selector": 'textarea[placeholder*="Message"]', "type": "css", "priority": 3, "description": "消息输入框"},
                        {"selector": '//textarea[contains(@placeholder, "Message")]', "type": "xpath", "priority": 4, "description": "消息输入框XPath"},
                        {"selector": 'textarea[placeholder*="Send a message"]', "type": "css", "priority": 5, "description": "发送消息框"},
                        {"selector": '#prompt-textarea', "type": "id", "priority": 6, "description": "提示输入框"},
                        {"selector": 'textarea', "type": "css", "priority": 7, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["Message ChatGPT", "Send a message", "Type a message"],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 2
                }

            # Claude / Anthropic
            elif 'claude.ai' in current_url or 'anthropic.com' in current_url:
                return {
                    "name": "Claude",
                    "selectors": [
                        {"selector": 'div[contenteditable="true"]', "type": "css", "priority": 1, "description": "可编辑输入框"},
                        {"selector": '//div[@contenteditable="true"]', "type": "xpath", "priority": 2, "description": "可编辑输入框XPath"},
                        {"selector": '.ProseMirror', "type": "class", "priority": 3, "description": "ProseMirror编辑器"},
                        {"selector": '//div[contains(@class, "ProseMirror")]', "type": "xpath", "priority": 4, "description": "ProseMirror编辑器XPath"},
                        {"selector": '[data-testid="chat-input"]', "type": "css", "priority": 5, "description": "聊天输入框"},
                        {"selector": '//div[@data-testid="chat-input"]', "type": "xpath", "priority": 6, "description": "聊天输入框XPath"},
                        {"selector": 'textarea', "type": "css", "priority": 7, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["Talk to Claude", "Type a message"],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 2
                }

            # Google Bard/Gemini
            elif 'bard.google.com' in current_url or 'gemini.google.com' in current_url:
                return {
                    "name": "Google Bard/Gemini",
                    "selectors": [
                        {"selector": 'rich-textarea[placeholder*="Enter a prompt"]', "type": "css", "priority": 1, "description": "富文本输入框"},
                        {"selector": '//rich-textarea[contains(@placeholder, "Enter a prompt")]', "type": "xpath", "priority": 2, "description": "富文本输入框XPath"},
                        {"selector": '.ql-editor', "type": "class", "priority": 3, "description": "Quill编辑器"},
                        {"selector": '//div[contains(@class, "ql-editor")]', "type": "xpath", "priority": 4, "description": "Quill编辑器XPath"},
                        {"selector": '[data-test-id="input-area"]', "type": "css", "priority": 5, "description": "输入区域"},
                        {"selector": 'textarea', "type": "css", "priority": 6, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["Enter a prompt here", "Ask Bard", "Enter your prompt"],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 2
                }

            # Coze (扣子)
            elif 'coze.cn' in current_url or 'coze.com' in current_url:
                return {
                    "name": "Coze扣子",
                    "selectors": [
                        # 最新的具体选择器（用户提供的实际选择器）
                        {"selector": '#root > div > div.flex.md\\:m-2 .flex-1.box-content.rounded.md\\:relative .md\\:w-auto .transition-all.duration-300 .ease-\\[cubic-bezier\\(0\\.65\\,0\\,0\\.35\\,0\\)\\] > div > div.rounded-xl.w-full.max-w-\\[800px\\].p-4.pt-0 > div > div:nth-child(1) > div > div.cm-scroller > div.cm-content.cm-lineWrapping > div', "type": "css", "priority": 1, "description": "Coze实际输入框"},
                        # 简化版本的选择器
                        {"selector": '.cm-content.cm-lineWrapping > div', "type": "css", "priority": 2, "description": "CodeMirror内容区域"},
                        {"selector": '.cm-content.cm-lineWrapping', "type": "css", "priority": 3, "description": "CodeMirror内容"},
                        {"selector": '.cm-scroller .cm-content', "type": "css", "priority": 4, "description": "CodeMirror滚动内容"},
                        {"selector": 'div[contenteditable="true"]', "type": "css", "priority": 5, "description": "可编辑div"},
                        # XPath版本
                        {"selector": '//div[contains(@class, "cm-content") and contains(@class, "cm-lineWrapping")]/div', "type": "xpath", "priority": 6, "description": "CodeMirror内容XPath"},
                        {"selector": '//div[contains(@class, "cm-content")]', "type": "xpath", "priority": 7, "description": "CodeMirror内容XPath简化"},
                        {"selector": '//div[@contenteditable="true"]', "type": "xpath", "priority": 8, "description": "可编辑div XPath"},
                        # 传统选择器作为备选
                        {"selector": 'textarea[placeholder*="输入"]', "type": "css", "priority": 9, "description": "输入框"},
                        {"selector": '//textarea[contains(@placeholder, "输入")]', "type": "xpath", "priority": 10, "description": "输入框XPath"},
                        {"selector": 'textarea[placeholder*="请输入"]', "type": "css", "priority": 11, "description": "请输入框"},
                        {"selector": '//textarea[contains(@placeholder, "请输入")]', "type": "xpath", "priority": 12, "description": "请输入框XPath"},
                        {"selector": '.chat-input textarea', "type": "css", "priority": 13, "description": "聊天输入框"},
                        {"selector": '//div[contains(@class, "chat-input")]//textarea', "type": "xpath", "priority": 14, "description": "聊天输入框XPath"},
                        {"selector": '[data-testid="chat-input"]', "type": "css", "priority": 15, "description": "测试ID输入框"},
                        {"selector": 'textarea', "type": "css", "priority": 16, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["输入消息", "请输入您的问题", "在此输入", "输入您的问题..."],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 1
                }

            # 通义千问
            elif 'tongyi.aliyun.com' in current_url or 'qianwen.aliyun.com' in current_url:
                return {
                    "name": "通义千问",
                    "selectors": [
                        {"selector": 'textarea[placeholder*="请输入"]', "type": "css", "priority": 1, "description": "请输入框"},
                        {"selector": '//textarea[contains(@placeholder, "请输入")]', "type": "xpath", "priority": 2, "description": "请输入框XPath"},
                        {"selector": '.input-area textarea', "type": "css", "priority": 3, "description": "输入区域"},
                        {"selector": '[data-testid="input"]', "type": "css", "priority": 4, "description": "输入测试ID"},
                        {"selector": 'textarea', "type": "css", "priority": 5, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["请输入您的问题", "输入问题", "请描述您的需求"],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 1
                }

            # 文心一言
            elif 'yiyan.baidu.com' in current_url:
                return {
                    "name": "文心一言",
                    "selectors": [
                        {"selector": 'textarea[placeholder*="请输入"]', "type": "css", "priority": 1, "description": "请输入框"},
                        {"selector": '//textarea[contains(@placeholder, "请输入")]', "type": "xpath", "priority": 2, "description": "请输入框XPath"},
                        {"selector": '.chat-input textarea', "type": "css", "priority": 3, "description": "聊天输入框"},
                        {"selector": 'textarea', "type": "css", "priority": 4, "description": "通用textarea"}
                    ],
                    "placeholder_texts": ["请输入您的问题", "输入问题"],
                    "submit_methods": ["enter", "button"],
                    "wait_time": 1
                }

            # 默认通用配置
            else:
                logger.debug(f"未识别的网站: {current_url}，使用通用配置")
                return self._get_default_config()

        except Exception as e:
            logger.debug(f"获取网站配置失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认通用配置"""
        return {
            "name": "通用网站",
            "selectors": [
                {"selector": 'input[type="text"]', "type": "css", "priority": 1, "description": "文本输入框"},
                {"selector": 'input[type="search"]', "type": "css", "priority": 2, "description": "搜索输入框"},
                {"selector": 'textarea', "type": "css", "priority": 3, "description": "文本区域"},
                {"selector": '[contenteditable="true"]', "type": "css", "priority": 4, "description": "可编辑元素"},
                {"selector": '//input[@type="text"]', "type": "xpath", "priority": 5, "description": "文本输入框XPath"},
                {"selector": '//textarea', "type": "xpath", "priority": 6, "description": "文本区域XPath"},
                {"selector": '//div[@contenteditable="true"]', "type": "xpath", "priority": 7, "description": "可编辑元素XPath"}
            ],
            "placeholder_texts": ["输入", "请输入", "Enter", "Type here"],
            "submit_methods": ["enter", "button"],
            "wait_time": 1
        }

    def get_website_specific_selectors(self) -> List[str]:
        """
        根据当前网站获取特定的输入框选择器（兼容旧接口）

        返回: 当前网站优化的选择器列表
        """
        config = self.get_website_config()
        # 按优先级排序并提取选择器
        selectors = sorted(config["selectors"], key=lambda x: x["priority"])
        return [item["selector"] for item in selectors]

    def find_input_element_smart(self, custom_selector: Optional[str] = None,
                                use_website_specific: bool = True,
                                use_position_click: bool = True) -> Optional[Any]:
        """
        智能查找输入框元素（优先使用指定选择器，找不到时自动回退，支持位置点击）

        参数:
            custom_selector - 自定义选择器，优先使用
            use_website_specific - 是否使用网站特定配置
            use_position_click - 是否使用位置点击作为最后回退方案
        返回: 找到的输入元素或None

        使用思路：
        1. 如果指定了自定义选择器，优先尝试
        2. 如果自定义选择器失败，使用网站特定配置按优先级尝试
        3. 如果网站特定配置失败，使用通用选择器
        4. 如果所有选择器都失败，尝试位置点击激活输入框
        5. 记录查找过程和结果，便于调试

        使用例子：
        # 优先使用指定选择器，失败时自动回退（包括位置点击）
        element = handler.find_input_element_smart('#custom-input')

        # 直接使用网站特定配置，禁用位置点击
        element = handler.find_input_element_smart(use_position_click=False)

        # 仅使用位置点击
        element = handler.find_input_element_smart(custom_selector=None,
                                                  use_website_specific=False,
                                                  use_position_click=True)
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return None

            search_log = []  # 记录搜索过程

            # 第一阶段：尝试自定义选择器
            if custom_selector:
                search_log.append(f"阶段1: 尝试自定义选择器")
                logger.debug(f"优先尝试自定义选择器: {custom_selector}")

                try:
                    elements = self._find_elements_by_selector(custom_selector)
                    for element in elements:
                        if self._is_input_element_valid(element):
                            selector_type = self._detect_selector_type(custom_selector)
                            logger.info(f"✅ 找到输入框: {custom_selector} (类型: {selector_type})")
                            search_log.append(f"成功: 自定义选择器 {custom_selector}")
                            return element
                    search_log.append(f"失败: 自定义选择器 {custom_selector} - 未找到有效元素")
                except Exception as e:
                    search_log.append(f"失败: 自定义选择器 {custom_selector} - {str(e)}")
                    logger.debug(f"自定义选择器失败: {e}")

            # 第二阶段：使用网站特定配置
            if use_website_specific:
                search_log.append(f"阶段2: 尝试网站特定配置")
                config = self.get_website_config()
                website_name = config["name"]
                logger.debug(f"使用 {website_name} 特定配置，共 {len(config['selectors'])} 个选择器")

                # 按优先级尝试
                for selector_info in sorted(config["selectors"], key=lambda x: x["priority"]):
                    selector = selector_info["selector"]
                    description = selector_info["description"]

                    try:
                        elements = self._find_elements_by_selector(selector)
                        for element in elements:
                            if self._is_input_element_valid(element):
                                logger.info(f"✅ 找到输入框: {selector} ({description})")
                                search_log.append(f"成功: {website_name}配置 {selector}")
                                return element
                        search_log.append(f"失败: {selector} ({description}) - 未找到有效元素")
                    except Exception as e:
                        search_log.append(f"失败: {selector} ({description}) - {str(e)}")
                        logger.debug(f"选择器 {selector} 失败: {e}")

            # 第三阶段：使用通用选择器作为最后回退
            search_log.append(f"阶段3: 尝试通用选择器回退")
            logger.debug("使用通用选择器作为最后回退")

            for selector in self.common_input_selectors:
                try:
                    elements = self._find_elements_by_selector(selector)
                    for element in elements:
                        if self._is_input_element_valid(element):
                            logger.info(f"✅ 找到输入框(通用): {selector}")
                            search_log.append(f"成功: 通用选择器 {selector}")
                            return element
                    search_log.append(f"失败: 通用选择器 {selector} - 未找到有效元素")
                except Exception as e:
                    search_log.append(f"失败: 通用选择器 {selector} - {str(e)}")
                    logger.debug(f"通用选择器 {selector} 失败: {e}")

            # 第四阶段：使用位置点击作为最后回退方案
            if use_position_click:
                search_log.append(f"阶段4: 尝试位置点击激活")
                logger.debug("使用位置点击作为最后回退方案")

                try:
                    element = self.find_input_by_position_click()
                    if element:
                        logger.info("✅ 通过位置点击找到输入框")
                        search_log.append("成功: 位置点击激活")
                        return element
                    else:
                        search_log.append("失败: 位置点击激活 - 未找到有效元素")
                except Exception as e:
                    search_log.append(f"失败: 位置点击激活 - {str(e)}")
                    logger.debug(f"位置点击激活失败: {e}")

            # 记录完整的搜索日志
            logger.warning("未找到可用的输入框")
            logger.debug("搜索过程:")
            for log_entry in search_log:
                logger.debug(f"  {log_entry}")

            return None

        except Exception as e:
            logger.error(f"智能查找输入框时发生错误: {e}")
            return None

    def _is_input_element_valid(self, element: Any) -> bool:
        """
        检查元素是否为有效的输入元素

        参数: element - 要检查的元素
        返回: 是否为有效输入元素

        使用思路：
        1. 检查元素是否可见且可交互
        2. 检查元素类型是否为输入类型
        3. 额外的有效性验证
        """
        try:
            # 基本可见性和可交互性检查
            if not (element.is_displayed() and element.is_enabled()):
                return False

            # 检查元素类型
            tag_name = element.tag_name.lower()
            if tag_name in ['input', 'textarea']:
                return True

            # 检查可编辑属性
            if element.get_attribute('contenteditable') == 'true':
                return True

            # 检查是否有输入相关的角色或属性
            role = element.get_attribute('role')
            if role in ['textbox', 'searchbox']:
                return True

            return False

        except Exception as e:
            logger.debug(f"检查元素有效性失败: {e}")
            return False

    def find_input_by_position_click(self, click_positions: List[Dict[str, Any]] = None,
                                    timeout: int = 5) -> Optional[Any]:
        """
        通过位置点击激活输入框，然后获取激活的元素

        参数:
            click_positions - 点击位置列表，格式: [{"x": 100, "y": 200, "description": "描述"}, ...]
            timeout - 等待激活的超时时间
        返回: 激活的输入元素或None

        使用思路：
        1. 在指定位置点击，激活输入框
        2. 获取当前激活的元素（document.activeElement）
        3. 验证激活的元素是否为有效输入框
        4. 适用于复杂选择器或动态生成的输入框

        使用例子：
        # 使用默认位置点击
        element = handler.find_input_by_position_click()

        # 指定多个点击位置
        positions = [
            {"x": 400, "y": 300, "description": "页面中心"},
            {"x": 400, "y": 500, "description": "页面下方"}
        ]
        element = handler.find_input_by_position_click(positions)
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return None

            # 获取网站特定的点击位置
            if not click_positions:
                click_positions = self._get_website_click_positions()

            logger.debug(f"尝试 {len(click_positions)} 个点击位置激活输入框")

            for position in click_positions:
                try:
                    x, y = position["x"], position["y"]
                    description = position.get("description", f"位置({x}, {y})")

                    logger.debug(f"点击位置: {description} - ({x}, {y})")

                    # 使用JavaScript点击指定位置
                    self.browser_manager.driver.execute_script(f"""
                        var element = document.elementFromPoint({x}, {y});
                        if (element) {{
                            element.click();
                            element.focus();
                        }}
                    """)

                    # 等待一小段时间让元素激活
                    time.sleep(0.3)

                    # 获取当前激活的元素
                    active_element = self.browser_manager.driver.execute_script(
                        "return document.activeElement;"
                    )

                    if active_element and self._is_input_element_valid(active_element):
                        logger.info(f"✅ 通过位置点击找到输入框: {description}")
                        return active_element
                    else:
                        logger.debug(f"位置点击未激活有效输入框: {description}")

                except Exception as e:
                    logger.debug(f"位置点击失败 {position}: {e}")
                    continue

            logger.warning("所有位置点击都未能激活有效输入框")
            return None

        except Exception as e:
            logger.error(f"位置点击查找输入框失败: {e}")
            return None

    def _get_website_click_positions(self) -> List[Dict[str, Any]]:
        """
        获取当前网站的推荐点击位置

        返回: 点击位置列表

        使用思路：
        1. 根据不同网站的布局特点，提供推荐的点击位置
        2. 这些位置通常是输入框的常见位置
        3. 按优先级排序，优先尝试最可能的位置
        """
        try:
            if not self.browser_manager.driver:
                return self._get_default_click_positions()

            current_url = self.browser_manager.driver.current_url.lower()

            # 获取页面尺寸
            window_size = self.browser_manager.driver.get_window_size()
            width, height = window_size["width"], window_size["height"]

            # ChatGPT / OpenAI
            if 'chat.openai.com' in current_url or 'openai.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 100, "description": "ChatGPT底部输入区域"},
                    {"x": width // 2, "y": height - 150, "description": "ChatGPT输入框区域"},
                    {"x": width // 2, "y": height - 200, "description": "ChatGPT输入框上方"},
                ]

            # Claude / Anthropic
            elif 'claude.ai' in current_url or 'anthropic.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 120, "description": "Claude底部输入区域"},
                    {"x": width // 2, "y": height - 180, "description": "Claude输入框区域"},
                    {"x": width // 2, "y": height - 240, "description": "Claude输入框上方"},
                ]

            # Coze (扣子) - 特别优化
            elif 'coze.cn' in current_url or 'coze.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 80, "description": "Coze底部输入区域"},
                    {"x": width // 2, "y": height - 120, "description": "Coze输入框中心"},
                    {"x": width // 2, "y": height - 160, "description": "Coze输入框上方"},
                    {"x": width // 2 - 100, "y": height - 120, "description": "Coze输入框左侧"},
                    {"x": width // 2 + 100, "y": height - 120, "description": "Coze输入框右侧"},
                    {"x": 400, "y": height - 100, "description": "Coze固定位置1"},
                    {"x": 500, "y": height - 100, "description": "Coze固定位置2"},
                ]

            # Google Bard/Gemini
            elif 'bard.google.com' in current_url or 'gemini.google.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 100, "description": "Bard底部输入区域"},
                    {"x": width // 2, "y": height - 150, "description": "Bard输入框区域"},
                ]

            # 通义千问
            elif 'tongyi.aliyun.com' in current_url or 'qianwen.aliyun.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 90, "description": "通义千问底部输入区域"},
                    {"x": width // 2, "y": height - 140, "description": "通义千问输入框区域"},
                ]

            # 文心一言
            elif 'yiyan.baidu.com' in current_url:
                return [
                    {"x": width // 2, "y": height - 100, "description": "文心一言底部输入区域"},
                    {"x": width // 2, "y": height - 150, "description": "文心一言输入框区域"},
                ]

            # 默认位置
            else:
                return self._get_default_click_positions()

        except Exception as e:
            logger.debug(f"获取网站点击位置失败: {e}")
            return self._get_default_click_positions()

    def _get_default_click_positions(self) -> List[Dict[str, Any]]:
        """获取默认点击位置"""
        try:
            # 尝试获取窗口尺寸，如果失败则使用默认值
            if self.browser_manager.driver:
                window_size = self.browser_manager.driver.get_window_size()
                width, height = window_size["width"], window_size["height"]
            else:
                width, height = 1200, 800  # 默认尺寸

            return [
                {"x": width // 2, "y": height - 100, "description": "页面底部中心"},
                {"x": width // 2, "y": height - 150, "description": "页面底部偏上"},
                {"x": width // 2, "y": height // 2, "description": "页面中心"},
                {"x": width // 2, "y": height // 3, "description": "页面上方"},
            ]
        except:
            # 如果所有方法都失败，返回固定位置
            return [
                {"x": 600, "y": 700, "description": "固定位置1"},
                {"x": 600, "y": 650, "description": "固定位置2"},
                {"x": 600, "y": 400, "description": "固定位置3"},
            ]

    def wait_for_input_ready(self, selector: Optional[str] = None, timeout: int = 10,
                           use_website_specific: bool = True, use_smart_search: bool = True) -> bool:
        """
        等待输入框准备就绪（可见且可交互）

        参数:
            selector - 自定义输入框选择器（支持CSS、XPath等）
            timeout - 超时时间(秒)
            use_website_specific - 是否使用网站特定的选择器优化
        返回: 输入框是否准备就绪

        使用思路：
        1. 在页面加载或动态内容更新后使用
        2. 确保输入框完全可用后再进行操作
        3. 支持多种选择器类型（CSS、XPath、ID等）
        4. 根据当前网站智能选择最佳选择器

        使用例子：
        # 等待默认输入框准备就绪（使用网站特定选择器）
        if handler.wait_for_input_ready():
            handler.input_text("Hello")

        # 使用自定义CSS选择器
        if handler.wait_for_input_ready('#chat-input'):
            handler.input_text("Hello")

        # 使用XPath选择器
        if handler.wait_for_input_ready('//textarea[@placeholder="输入消息"]'):
            handler.input_text("Hello")
        """
        try:
            start_time = time.time()

            # 如果没有指定选择器且启用网站特定优化，使用网站特定选择器
            if not selector and use_website_specific:
                # 临时保存原始选择器
                original_selectors = self.common_input_selectors
                # 使用网站特定选择器
                self.common_input_selectors = self.get_website_specific_selectors()
                logger.debug(f"使用网站特定选择器，共 {len(self.common_input_selectors)} 个")

            attempt_count = 0
            while time.time() - start_time < timeout:
                attempt_count += 1

                # 选择查找方法
                if use_smart_search:
                    input_element = self.find_input_element_smart(selector, use_website_specific)
                else:
                    input_element = self.find_input_element(selector)

                if input_element:
                    # 额外验证元素确实可用
                    try:
                        # 尝试点击元素以确保其可交互
                        input_element.click()
                        logger.info(f"输入框已准备就绪 (尝试 {attempt_count} 次)")

                        # 恢复原始选择器（仅在非智能搜索模式下需要）
                        if not use_smart_search and not selector and use_website_specific:
                            self.common_input_selectors = original_selectors

                        return True
                    except Exception as e:
                        logger.debug(f"输入框点击测试失败: {e}")

                time.sleep(0.5)  # 等待0.5秒后重试

            # 恢复原始选择器（仅在非智能搜索模式下需要）
            if not use_smart_search and not selector and use_website_specific:
                self.common_input_selectors = original_selectors

            logger.warning(f"等待输入框准备就绪超时 ({timeout}秒，尝试 {attempt_count} 次)")
            return False

        except Exception as e:
            logger.error(f"等待输入框准备就绪时发生错误: {e}")

            # 确保恢复原始选择器
            if not selector and use_website_specific:
                try:
                    self.common_input_selectors = original_selectors
                except:
                    pass

            return False
