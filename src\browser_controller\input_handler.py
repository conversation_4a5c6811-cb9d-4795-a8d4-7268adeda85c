"""
输入处理器 - 负责处理用户输入和浏览器交互
当AI有询问时，控制浏览器进行输入和回车操作
"""

import time
from typing import Optional, List, Dict, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from loguru import logger

from .browser_manager import BrowserManager
from config import settings


class InputHandler:
    """输入处理器类"""
    
    def __init__(self, browser_manager: BrowserManager):
        """
        初始化输入处理器
        参数: browser_manager - 浏览器管理器实例
        """
        self.browser_manager = browser_manager
        self.config = settings.input
        self.common_input_selectors = self.config.input_selectors
        
    def find_input_element(self, custom_selector: Optional[str] = None) -> Optional[Any]:
        """
        查找页面中的输入框元素
        参数: custom_selector - 自定义选择器，如果提供则优先使用
        返回: 找到的输入元素或None
        
        使用思路：
        1. 如果提供了自定义选择器，优先使用
        2. 否则按照常见输入框选择器的优先级依次尝试
        3. 返回第一个找到的可见且可交互的输入框
        
        使用例子：
        handler = InputHandler(browser_manager)
        # 使用默认选择器查找
        input_elem = handler.find_input_element()
        # 使用自定义选择器查找
        input_elem = handler.find_input_element('#chat-input')
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return None
            
            selectors = [custom_selector] if custom_selector else self.common_input_selectors
            
            for selector in selectors:
                try:
                    # 查找元素
                    elements = self.browser_manager.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        # 检查元素是否可见且可交互
                        if element.is_displayed() and element.is_enabled():
                            logger.info(f"找到输入框: {selector}")
                            return element
                            
                except Exception as e:
                    logger.debug(f"选择器 {selector} 查找失败: {e}")
                    continue
            
            logger.warning("未找到可用的输入框")
            return None
            
        except Exception as e:
            logger.error(f"查找输入框时发生错误: {e}")
            return None
    
    def input_text(self, text: str, selector: Optional[str] = None, clear_first: bool = True) -> bool:
        """
        在输入框中输入文本
        参数:
            text - 要输入的文本内容
            selector - 输入框选择器，为空则自动查找
            clear_first - 是否先清空输入框，默认为True
        返回: 输入是否成功
        
        使用思路：
        1. 查找输入框元素
        2. 可选择性清空现有内容
        3. 输入新文本
        4. 验证输入结果
        
        使用例子：
        # 基本使用
        success = handler.input_text("Hello World")
        # 指定输入框并保留原有内容
        success = handler.input_text("追加内容", "#chat-input", clear_first=False)
        """
        try:
            # 查找输入框
            input_element = self.find_input_element(selector)
            if not input_element:
                return False
            
            # 聚焦到输入框
            input_element.click()
            time.sleep(0.1)  # 短暂等待确保聚焦

            # 清空输入框（如果需要）
            clear_first = clear_first if clear_first is not None else self.config.clear_before_input
            if clear_first:
                input_element.clear()
                # 对于某些特殊输入框，使用Ctrl+A + Delete
                input_element.send_keys(Keys.CONTROL + "a")
                input_element.send_keys(Keys.DELETE)

            # 输入文本（带延迟）
            if self.config.input_delay > 0:
                for char in text:
                    input_element.send_keys(char)
                    time.sleep(self.config.input_delay)
            else:
                input_element.send_keys(text)
            
            # 验证输入结果
            current_value = input_element.get_attribute("value") or input_element.text
            if text in current_value:
                logger.info(f"文本输入成功: {text[:50]}...")
                return True
            else:
                logger.warning(f"文本输入可能不完整，期望包含: {text[:50]}..., 实际: {current_value[:50]}...")
                return True  # 某些情况下验证可能不准确，但输入操作已执行
                
        except Exception as e:
            logger.error(f"输入文本失败: {e}")
            return False

    def press_enter(self, selector: Optional[str] = None) -> bool:
        """
        在输入框中按回车键
        参数: selector - 输入框选择器，为空则自动查找
        返回: 操作是否成功

        使用思路：
        1. 查找输入框元素
        2. 确保输入框处于聚焦状态
        3. 发送回车键

        使用例子：
        # 在当前聚焦的输入框按回车
        success = handler.press_enter()
        # 在指定输入框按回车
        success = handler.press_enter("#search-box")
        """
        try:
            # 查找输入框
            input_element = self.find_input_element(selector)
            if not input_element:
                return False

            # 确保输入框聚焦
            input_element.click()
            time.sleep(0.1)

            # 按回车键
            input_element.send_keys(Keys.RETURN)

            logger.info("回车键发送成功")
            return True

        except Exception as e:
            logger.error(f"发送回车键失败: {e}")
            return False

    def input_and_submit(self, text: str, selector: Optional[str] = None, clear_first: bool = True) -> bool:
        """
        输入文本并按回车提交
        参数:
            text - 要输入的文本
            selector - 输入框选择器
            clear_first - 是否先清空输入框
        返回: 操作是否成功

        使用思路：
        1. 先输入文本
        2. 然后按回车提交
        3. 这是AI询问时的常用操作组合

        使用例子：
        # 输入问题并提交
        success = handler.input_and_submit("什么是人工智能？")
        """
        try:
            # 先输入文本
            if not self.input_text(text, selector, clear_first):
                return False

            # 短暂等待确保输入完成
            time.sleep(0.2)

            # 按回车提交
            return self.press_enter(selector)

        except Exception as e:
            logger.error(f"输入并提交失败: {e}")
            return False

    def wait_for_input_ready(self, selector: Optional[str] = None, timeout: int = 10) -> bool:
        """
        等待输入框准备就绪（可见且可交互）
        参数:
            selector - 输入框选择器
            timeout - 超时时间(秒)
        返回: 输入框是否准备就绪

        使用思路：
        1. 在页面加载或动态内容更新后使用
        2. 确保输入框完全可用后再进行操作

        使用例子：
        # 等待默认输入框准备就绪
        if handler.wait_for_input_ready():
            handler.input_text("Hello")
        """
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                input_element = self.find_input_element(selector)
                if input_element:
                    logger.info("输入框已准备就绪")
                    return True

                time.sleep(0.5)  # 等待0.5秒后重试

            logger.warning(f"等待输入框准备就绪超时 ({timeout}秒)")
            return False

        except Exception as e:
            logger.error(f"等待输入框准备就绪时发生错误: {e}")
            return False
