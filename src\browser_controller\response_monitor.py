"""
响应监听器 - 监听浏览器中的HTTP响应和页面变化
用于捕获AI回复或其他动态内容更新
"""

import time
import json
from typing import Optional, Dict, Any, List, Callable
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from loguru import logger

from .browser_manager import BrowserManager


class ResponseMonitor:
    """响应监听器类"""
    
    def __init__(self, browser_manager: BrowserManager):
        """
        初始化响应监听器
        参数: browser_manager - 浏览器管理器实例
        """
        self.browser_manager = browser_manager
        self.response_callbacks: List[Callable] = []
        self.monitoring = False
        self.config = settings.response

        # 使用配置的响应内容选择器
        self.response_selectors = self.config.response_selectors
    
    def add_response_callback(self, callback: Callable[[str], None]):
        """
        添加响应回调函数
        参数: callback - 当检测到新响应时调用的回调函数
        
        使用思路：
        1. 注册回调函数来处理检测到的响应
        2. 回调函数接收响应文本作为参数
        
        使用例子：
        def handle_response(response_text):
            print(f"收到响应: {response_text}")
        
        monitor = ResponseMonitor(browser_manager)
        monitor.add_response_callback(handle_response)
        """
        self.response_callbacks.append(callback)
        logger.info("已添加响应回调函数")
    
    def wait_for_response(self, timeout: Optional[int] = None, custom_selector: Optional[str] = None) -> Optional[str]:
        """
        等待页面中出现新的响应内容
        参数:
            timeout - 超时时间(秒)
            custom_selector - 自定义响应内容选择器
        返回: 响应文本内容或None
        
        使用思路：
        1. 记录当前页面状态
        2. 等待新内容出现
        3. 返回新增的响应内容
        
        使用例子：
        # 等待默认响应
        response = monitor.wait_for_response()
        # 等待特定元素的响应
        response = monitor.wait_for_response(60, '.ai-answer')
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return None
            
            # 使用配置的超时时间
            timeout = timeout or self.config.response_timeout

            # 记录初始状态
            initial_content = self._get_current_responses(custom_selector)
            initial_count = len(initial_content)

            logger.info(f"开始等待响应，当前响应数量: {initial_count}，超时时间: {timeout}秒")

            start_time = time.time()
            last_content = ""
            stable_start_time = None

            while time.time() - start_time < timeout:
                try:
                    # 检查是否有新响应
                    current_content = self._get_current_responses(custom_selector)

                    if len(current_content) > initial_count:
                        # 找到新响应
                        new_response = current_content[-1]  # 获取最新的响应

                        # 检查响应长度是否符合要求
                        if len(new_response) < self.config.min_response_length:
                            logger.debug(f"响应长度不足，跳过: {len(new_response)} < {self.config.min_response_length}")
                            time.sleep(self.config.response_check_interval)
                            continue

                        if len(new_response) > self.config.max_response_length:
                            logger.warning(f"响应长度超限，截断: {len(new_response)} > {self.config.max_response_length}")
                            new_response = new_response[:self.config.max_response_length]

                        # 检查响应稳定性
                        if new_response == last_content:
                            if stable_start_time is None:
                                stable_start_time = time.time()
                            elif time.time() - stable_start_time >= self.config.response_stable_time:
                                # 响应已稳定
                                processed_response = self._process_response(new_response)
                                logger.info(f"检测到稳定响应: {processed_response[:100]}...")

                                # 触发回调函数
                                for callback in self.response_callbacks:
                                    try:
                                        callback(processed_response)
                                    except Exception as e:
                                        logger.error(f"响应回调函数执行失败: {e}")

                                return processed_response
                        else:
                            # 响应内容变化，重置稳定计时
                            last_content = new_response
                            stable_start_time = None

                    time.sleep(self.config.response_check_interval)
                    
                except Exception as e:
                    logger.debug(f"检查响应时发生错误: {e}")
                    time.sleep(1)
                    continue
            
            logger.warning(f"等待响应超时 ({timeout}秒)")
            return None
            
        except Exception as e:
            logger.error(f"等待响应时发生错误: {e}")
            return None
    
    def _get_current_responses(self, custom_selector: Optional[str] = None) -> List[str]:
        """
        获取当前页面中的所有响应内容
        参数: custom_selector - 自定义选择器
        返回: 响应文本列表
        """
        responses = []
        
        try:
            selectors = [custom_selector] if custom_selector else self.response_selectors
            
            for selector in selectors:
                try:
                    elements = self.browser_manager.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and text not in responses:
                                responses.append(text)
                                
                except Exception as e:
                    logger.debug(f"获取响应内容失败 {selector}: {e}")
                    continue
            
            return responses
            
        except Exception as e:
            logger.error(f"获取当前响应失败: {e}")
            return []
    
    def monitor_network_responses(self, filter_url: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        监听网络响应（需要启用Chrome DevTools Protocol）
        参数: filter_url - 过滤特定URL的响应
        返回: 网络响应列表
        
        使用思路：
        1. 启用网络监听
        2. 捕获HTTP响应
        3. 过滤和处理响应数据
        
        使用例子：
        # 监听所有网络响应
        responses = monitor.monitor_network_responses()
        # 监听特定API的响应
        api_responses = monitor.monitor_network_responses("/api/chat")
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return []
            
            # 启用网络域
            self.browser_manager.driver.execute_cdp_cmd('Network.enable', {})
            
            # 获取网络日志
            logs = self.browser_manager.driver.get_log('performance')
            
            responses = []
            for log in logs:
                try:
                    message = json.loads(log['message'])
                    
                    if message['message']['method'] == 'Network.responseReceived':
                        response_data = message['message']['params']
                        response_url = response_data['response']['url']
                        
                        # 应用URL过滤
                        if filter_url and filter_url not in response_url:
                            continue
                        
                        responses.append({
                            'url': response_url,
                            'status': response_data['response']['status'],
                            'headers': response_data['response']['headers'],
                            'timestamp': log['timestamp']
                        })
                        
                except Exception as e:
                    logger.debug(f"解析网络日志失败: {e}")
                    continue
            
            return responses
            
        except Exception as e:
            logger.error(f"监听网络响应失败: {e}")
            return []
    
    def wait_for_element_change(self, selector: str, timeout: int = 30) -> bool:
        """
        等待指定元素内容发生变化
        参数:
            selector - 元素选择器
            timeout - 超时时间(秒)
        返回: 是否检测到变化
        
        使用思路：
        1. 记录元素初始状态
        2. 持续监听元素变化
        3. 检测到变化时返回True
        
        使用例子：
        # 等待聊天区域内容更新
        changed = monitor.wait_for_element_change('.chat-container')
        """
        try:
            if not self.browser_manager.driver:
                logger.error("浏览器未启动")
                return False
            
            # 获取初始内容
            try:
                element = self.browser_manager.driver.find_element(By.CSS_SELECTOR, selector)
                initial_content = element.text
                initial_html = element.get_attribute('innerHTML')
            except Exception:
                logger.warning(f"初始元素未找到: {selector}")
                initial_content = ""
                initial_html = ""
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    element = self.browser_manager.driver.find_element(By.CSS_SELECTOR, selector)
                    current_content = element.text
                    current_html = element.get_attribute('innerHTML')
                    
                    # 检查内容或HTML是否发生变化
                    if current_content != initial_content or current_html != initial_html:
                        logger.info(f"检测到元素变化: {selector}")
                        return True
                    
                except Exception:
                    # 元素可能暂时不存在，继续等待
                    pass
                
                time.sleep(0.5)  # 每0.5秒检查一次
            
            logger.warning(f"等待元素变化超时: {selector}")
            return False
            
        except Exception as e:
            logger.error(f"等待元素变化时发生错误: {e}")
            return False

    def _process_response(self, response: str) -> str:
        """
        处理响应文本

        参数: response - 原始响应文本
        返回: 处理后的响应文本

        使用思路：
        1. 根据配置清理响应文本
        2. 移除HTML标签
        3. 规范化空白字符
        """
        if not self.config.clean_response_text:
            return response

        processed = response

        # 移除HTML标签
        if self.config.remove_html_tags:
            import re
            processed = re.sub(r'<[^>]+>', '', processed)

        # 规范化空白字符
        if self.config.normalize_whitespace:
            import re
            # 将多个空白字符替换为单个空格
            processed = re.sub(r'\s+', ' ', processed)
            # 移除首尾空白
            processed = processed.strip()

        # 检查是否为空响应
        if self.config.ignore_empty_responses and not processed.strip():
            return ""

        return processed
