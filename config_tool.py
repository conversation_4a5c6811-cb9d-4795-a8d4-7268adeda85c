"""
配置工具 - 帮助用户检测和配置浏览器路径
自动检测系统中的浏览器并生成配置文件
"""

import os
import sys
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional


class BrowserDetector:
    """浏览器检测器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.detected_browsers = {}
        
    def detect_all_browsers(self) -> Dict[str, Optional[str]]:
        """
        检测所有支持的浏览器
        返回: 浏览器名称到路径的映射
        """
        browsers = {
            'chrome': self.detect_chrome(),
            'firefox': self.detect_firefox(),
            'edge': self.detect_edge()
        }
        
        self.detected_browsers = browsers
        return browsers
    
    def detect_chrome(self) -> Optional[str]:
        """检测Chrome浏览器路径"""
        if self.system == "windows":
            possible_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
            ]
        elif self.system == "darwin":  # macOS
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser",
                "/snap/bin/chromium",
            ]
        
        return self._find_executable(possible_paths)
    
    def detect_firefox(self) -> Optional[str]:
        """检测Firefox浏览器路径"""
        if self.system == "windows":
            possible_paths = [
                r"C:\Program Files\Mozilla Firefox\firefox.exe",
                r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe",
                os.path.expanduser(r"~\AppData\Local\Mozilla Firefox\firefox.exe"),
            ]
        elif self.system == "darwin":  # macOS
            possible_paths = [
                "/Applications/Firefox.app/Contents/MacOS/firefox",
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/firefox",
                "/usr/bin/firefox-esr",
                "/snap/bin/firefox",
            ]
        
        return self._find_executable(possible_paths)
    
    def detect_edge(self) -> Optional[str]:
        """检测Edge浏览器路径"""
        if self.system == "windows":
            possible_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                os.path.expanduser(r"~\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
            ]
        elif self.system == "darwin":  # macOS
            possible_paths = [
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
            ]
        else:  # Linux
            possible_paths = [
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-stable",
                "/opt/microsoft/msedge/msedge",
            ]
        
        return self._find_executable(possible_paths)
    
    def _find_executable(self, paths: List[str]) -> Optional[str]:
        """在给定路径列表中查找可执行文件"""
        for path in paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                return path
        return None
    
    def get_browser_version(self, browser_path: str, browser_type: str) -> Optional[str]:
        """获取浏览器版本"""
        try:
            if browser_type == "chrome":
                if self.system == "windows":
                    result = subprocess.run([browser_path, "--version"], 
                                          capture_output=True, text=True, timeout=5)
                else:
                    result = subprocess.run([browser_path, "--version"], 
                                          capture_output=True, text=True, timeout=5)
            elif browser_type == "firefox":
                result = subprocess.run([browser_path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
            elif browser_type == "edge":
                result = subprocess.run([browser_path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
            else:
                return None
            
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            print(f"获取{browser_type}版本失败: {e}")
        
        return None


def generate_env_config(detected_browsers: Dict[str, Optional[str]]) -> str:
    """生成.env配置内容"""
    config_lines = []
    
    # 添加浏览器路径配置
    config_lines.append("# ==================== 浏览器路径配置 ====================")
    config_lines.append("# 自动检测到的浏览器路径，如果检测失败请手动设置")
    config_lines.append("")
    
    for browser, path in detected_browsers.items():
        if path:
            config_lines.append(f"# {browser.title()}浏览器路径")
            config_lines.append(f"BROWSER__{browser.upper()}_BINARY_PATH={path}")
            config_lines.append("")
        else:
            config_lines.append(f"# {browser.title()}浏览器未检测到，请手动设置路径")
            config_lines.append(f"# BROWSER__{browser.upper()}_BINARY_PATH=")
            config_lines.append("")
    
    return "\n".join(config_lines)


def main():
    """主函数"""
    print("🔍 Browser AI 配置工具")
    print("=" * 50)
    
    # 检测浏览器
    print("正在检测系统中的浏览器...")
    detector = BrowserDetector()
    browsers = detector.detect_all_browsers()
    
    print(f"\n系统信息: {platform.system()} {platform.release()}")
    print("\n检测结果:")
    print("-" * 30)
    
    found_browsers = []
    for browser, path in browsers.items():
        if path:
            print(f"✅ {browser.title()}: {path}")
            # 尝试获取版本信息
            version = detector.get_browser_version(path, browser)
            if version:
                print(f"   版本: {version}")
            found_browsers.append(browser)
        else:
            print(f"❌ {browser.title()}: 未找到")
    
    if not found_browsers:
        print("\n⚠️ 未检测到任何浏览器，请手动安装浏览器后重新运行此工具")
        return
    
    print(f"\n✅ 检测到 {len(found_browsers)} 个浏览器")
    
    # 询问用户是否要生成配置
    print("\n" + "=" * 50)
    choice = input("是否要生成配置文件？(y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        # 生成配置
        config_content = generate_env_config(browsers)
        
        # 读取现有的.env文件
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        else:
            existing_content = ""
        
        # 检查是否已有浏览器路径配置
        if "BROWSER__CHROME_BINARY_PATH" in existing_content:
            choice = input("检测到现有的浏览器配置，是否覆盖？(y/n): ").lower().strip()
            if choice not in ['y', 'yes', '是']:
                print("配置生成已取消")
                return
        
        # 写入配置文件
        backup_file = Path(".env.backup")
        if env_file.exists():
            # 备份现有配置
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(existing_content)
            print(f"已备份现有配置到: {backup_file}")
        
        # 合并配置
        if existing_content and not existing_content.endswith('\n'):
            existing_content += '\n'
        
        new_content = existing_content + "\n" + config_content
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 配置已写入: {env_file}")
        print("\n推荐的下一步:")
        print("1. 检查生成的.env文件，确认浏览器路径正确")
        print("2. 运行 python start.py --check-deps 检查依赖")
        print("3. 运行 python start.py --headless 测试服务")
        
    else:
        print("配置生成已跳过")
    
    # 显示手动配置说明
    print("\n" + "=" * 50)
    print("💡 手动配置说明:")
    print("如果自动检测失败，你可以手动在.env文件中设置浏览器路径：")
    print("")
    
    for browser in ['chrome', 'firefox', 'edge']:
        print(f"# {browser.title()}浏览器路径")
        print(f"BROWSER__{browser.upper()}_BINARY_PATH=/path/to/{browser}")
        print("")
    
    print("更多配置选项请参考 config_example.py 文件")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 配置工具已退出")
    except Exception as e:
        print(f"\n❌ 配置工具运行出错: {e}")
        sys.exit(1)
