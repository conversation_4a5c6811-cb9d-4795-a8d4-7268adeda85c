# API服务配置
API_HOST=localhost
API_PORT=8000
API_TITLE=Browser AI API
API_VERSION=1.0.0

# AI模型配置
MODEL_NAME=gpt-3.5-turbo
MAX_TOKENS=2048
TEMPERATURE=0.7

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=browser_ai.log
LOG_ROTATION=10 MB
LOG_RETENTION=7 days

# 调试模式
DEBUG=false

# 性能配置
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# ==================== 浏览器配置 ====================
# 基本浏览器配置
BROWSER__BROWSER_TYPE=chrome
BROWSER__BROWSER_HEADLESS=false
BROWSER__BROWSER_TIMEOUT=30
BROWSER__BROWSER_WINDOW_SIZE=1920,1080

# 浏览器路径配置 (当自动检测失败时使用)
# Windows示例路径:
# BROWSER__CHROME_BINARY_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
# BROWSER__FIREFOX_BINARY_PATH=C:\Program Files\Mozilla Firefox\firefox.exe
# BROWSER__EDGE_BINARY_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe

# Linux示例路径:
# BROWSER__CHROME_BINARY_PATH=/usr/bin/google-chrome
# BROWSER__FIREFOX_BINARY_PATH=/usr/bin/firefox
# BROWSER__EDGE_BINARY_PATH=/usr/bin/microsoft-edge

# macOS示例路径:
# BROWSER__CHROME_BINARY_PATH=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
# BROWSER__FIREFOX_BINARY_PATH=/Applications/Firefox.app/Contents/MacOS/firefox

# 驱动程序路径配置 (通常不需要手动设置，webdriver-manager会自动下载)
# BROWSER__CHROME_DRIVER_PATH=
# BROWSER__FIREFOX_DRIVER_PATH=
# BROWSER__EDGE_DRIVER_PATH=

# 页面加载配置
BROWSER__PAGE_LOAD_TIMEOUT=30
BROWSER__IMPLICIT_WAIT=10
BROWSER__SCRIPT_TIMEOUT=30

# 浏览器启动重试配置
BROWSER__BROWSER_START_RETRY_COUNT=3
BROWSER__BROWSER_START_RETRY_DELAY=2

# ==================== 输入配置 ====================
# 输入行为配置
INPUT__INPUT_DELAY=0.1
INPUT__CLEAR_BEFORE_INPUT=true
INPUT__INPUT_RETRY_COUNT=3
INPUT__INPUT_RETRY_DELAY=1.0

# ==================== 响应配置 ====================
# 响应等待配置
RESPONSE__RESPONSE_TIMEOUT=60
RESPONSE__RESPONSE_CHECK_INTERVAL=0.5
RESPONSE__RESPONSE_STABLE_TIME=2.0

# 响应过滤配置
RESPONSE__MIN_RESPONSE_LENGTH=5
RESPONSE__MAX_RESPONSE_LENGTH=50000
RESPONSE__IGNORE_EMPTY_RESPONSES=true

# 响应处理配置
RESPONSE__CLEAN_RESPONSE_TEXT=true
RESPONSE__REMOVE_HTML_TAGS=true
RESPONSE__NORMALIZE_WHITESPACE=true
