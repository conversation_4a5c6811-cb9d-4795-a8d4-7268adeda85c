"""
网页版聊天界面演示脚本
展示如何使用Browser AI的网页界面进行对话测试
"""

import requests
import time
import json
import webbrowser
from typing import Dict, Any


class BrowserAIDemo:
    """Browser AI 演示类"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        """
        初始化演示类
        参数: base_url - API服务地址
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = 30
        
    def check_service_status(self) -> bool:
        """检查服务状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务状态: {data['status']}")
                print(f"   版本: {data['version']}")
                print(f"   浏览器: {data['config']['browser_type']}")
                return True
            else:
                print(f"❌ 服务状态异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False
    
    def check_browser_status(self) -> Dict[str, Any]:
        """检查浏览器状态"""
        try:
            response = requests.get(f"{self.base_url}/browser/status", timeout=30)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 浏览器状态:")
                print(f"   运行中: {data['running']}")
                print(f"   类型: {data['browser_type']}")
                print(f"   当前URL: {data.get('current_url', 'N/A')}")
                return data
            else:
                print(f"❌ 浏览器状态检查失败: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ 浏览器状态检查异常: {e}")
            return {}
    
    def navigate_to_ai_site(self, url: str = "https://www.baidu.com") -> bool:
        """导航到AI网站"""
        try:
            print(f"🌐 导航到: {url}")
            data = {"url": url, "wait_for_load": True}
            response = requests.post(
                f"{self.base_url}/browser/navigate",
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 导航成功")
                    return True
                else:
                    print(f"❌ 导航失败: {result}")
                    return False
            else:
                print(f"❌ 导航请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 导航异常: {e}")
            return False
    
    def test_chat_api(self, message: str = "你好，请简单介绍一下自己") -> str:
        """测试聊天API"""
        try:
            print(f"💬 发送消息: {message}")
            
            data = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": message}],
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["choices"][0]["message"]["content"]
                print(f"🤖 AI回复: {ai_response[:200]}...")
                return ai_response
            else:
                error_msg = f"聊天API失败: {response.status_code}"
                print(f"❌ {error_msg}")
                return error_msg
                
        except Exception as e:
            error_msg = f"聊天API异常: {e}"
            print(f"❌ {error_msg}")
            return error_msg
    
    def open_web_interface(self):
        """打开网页界面"""
        try:
            chat_url = f"{self.base_url}/chat"
            print(f"🌐 打开网页界面: {chat_url}")
            webbrowser.open(chat_url)
            return True
        except Exception as e:
            print(f"❌ 打开网页界面失败: {e}")
            return False
    
    def run_demo(self):
        """运行完整演示"""
        print("🚀 Browser AI 网页版聊天界面演示")
        print("=" * 60)
        
        # 1. 检查服务状态
        print("\n1. 检查服务状态...")
        if not self.check_service_status():
            print("❌ 服务未启动，请先运行: python start.py --port 8002")
            return False
        
        # 2. 检查浏览器状态
        print("\n2. 检查浏览器状态...")
        browser_status = self.check_browser_status()
        if not browser_status.get('running'):
            print("⚠️ 浏览器未运行，将在API调用时自动启动")
        
        # 3. 导航到测试网站
        print("\n3. 导航到测试网站...")
        if not self.navigate_to_ai_site():
            print("⚠️ 导航失败，但可以继续测试API")
        
        # 4. 再次检查浏览器状态
        print("\n4. 再次检查浏览器状态...")
        self.check_browser_status()
        
        # 5. 打开网页界面
        print("\n5. 打开网页界面...")
        if self.open_web_interface():
            print("✅ 网页界面已在浏览器中打开")
        
        # 6. 演示说明
        print("\n" + "=" * 60)
        print("🎉 演示准备完成！")
        print("\n📋 接下来您可以：")
        print("1. 在打开的网页界面中测试对话功能")
        print("2. 观察服务器日志中的浏览器操作")
        print("3. 尝试不同的问题和对话")
        
        print("\n💡 使用提示：")
        print("• 网页界面会自动检测服务状态")
        print("• 绿色指示灯表示服务正常")
        print("• 可以看到浏览器类型和运行状态")
        print("• 输入消息后点击发送或按回车")
        
        print("\n🔧 注意事项：")
        print("• 当前配置为测试模式，需要先导航到真实的AI网站")
        print("• 输入框和响应选择器需要根据目标网站调整")
        print("• 可以在config.py或.env文件中自定义配置")
        
        print("\n📊 API端点：")
        print(f"• 聊天接口: {self.base_url}/v1/chat/completions")
        print(f"• 浏览器控制: {self.base_url}/browser/navigate")
        print(f"• 健康检查: {self.base_url}/health")
        print(f"• API文档: {self.base_url}/docs")
        
        return True


def main():
    """主函数"""
    try:
        # 创建演示实例
        demo = BrowserAIDemo("http://localhost:8002")
        
        # 运行演示
        success = demo.run_demo()
        
        if success:
            print("\n🎊 演示完成！请在网页界面中测试对话功能。")
            print("按 Ctrl+C 可以停止服务")
            
            # 保持脚本运行，让用户测试
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 演示结束")
        else:
            print("\n⚠️ 演示初始化失败，请检查服务状态")
            
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
