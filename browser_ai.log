2025-07-17 21:55:23 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 21:55:23 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 21:55:23 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 21:55:23 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 21:55:23 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 21:55:23 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 21:55:23 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 21:55:23 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 22:03:05 | ERROR    | src.browser_controller.browser_manager:start_browser:61 | 启动浏览器失败: Message: session not created: This version of ChromeDriver only supports Chrome version 78
Stacktrace:
Backtrace:
	Ordinal0 [0x0018A923+1550627]
	Ordinal0 [0x0010A731+1025841]
	Ordinal0 [0x0008C715+509717]
	Ordinal0 [0x0001FC68+64616]
	Ordinal0 [0x0001C594+50580]
	Ordinal0 [0x0003ACE7+175335]
	Ordinal0 [0x0003A8ED+174317]
	Ordinal0 [0x00038CDB+167131]
	Ordinal0 [0x0002144A+70730]
	Ordinal0 [0x000224D0+74960]
	Ordinal0 [0x00022469+74857]
	Ordinal0 [0x001242F7+1131255]
	GetHandleVerifier [0x0022711D+523789]
	GetHandleVerifier [0x00226EB0+523168]
	GetHandleVerifier [0x0022E207+552695]
	GetHandleVerifier [0x0022791A+525834]
	Ordinal0 [0x0011B82C+1095724]
	Ordinal0 [0x0012636B+1139563]
	Ordinal0 [0x001264D3+1139923]
	Ordinal0 [0x00125455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]
	(No symbol) [0x00000000]

2025-07-17 22:03:09 | ERROR    | src.api.browser_api:get_browser_status:111 | 获取浏览器状态失败: 
2025-07-17 22:03:10 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 22:03:10 | INFO     | main:lifespan:58 | 浏览器API实例已关闭
2025-07-17 22:03:10 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 22:03:11 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 22:03:11 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 22:03:11 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 22:03:11 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 22:03:11 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 22:03:11 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 22:03:11 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 22:03:11 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:15:05 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:15:05 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:15:05 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:15:05 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:15:05 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 23:16:10 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:16:10 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:16:10 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:16:10 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:16:10 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 23:16:10 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:16:10 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 23:16:10 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:17:00 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:17:00 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:17:01 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:17:01 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:17:01 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:17:01 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:17:01 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 23:17:01 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:17:01 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 23:17:01 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:17:22 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:17:22 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:17:22 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:17:22 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:17:22 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8000
2025-07-17 23:17:22 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:17:22 | INFO     | main:lifespan:38 |   - 无头模式: True
2025-07-17 23:17:22 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:17:22 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:17:22 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:17:41 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:17:41 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:17:41 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:17:41 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:17:41 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8001
2025-07-17 23:17:41 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:17:41 | INFO     | main:lifespan:38 |   - 无头模式: True
2025-07-17 23:17:41 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:18:16 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:18:16 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:18:36 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:18:36 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:18:36 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:18:36 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:18:36 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8001
2025-07-17 23:18:36 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:18:36 | INFO     | main:lifespan:38 |   - 无头模式: True
2025-07-17 23:18:36 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:22:13 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:22:13 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:22:33 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:22:33 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:22:33 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:22:33 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:22:33 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8001
2025-07-17 23:22:33 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:22:33 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 23:22:33 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:22:48 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:22:48 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:23:44 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:23:44 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:23:44 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:23:44 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:23:44 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8001
2025-07-17 23:23:44 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:23:44 | INFO     | main:lifespan:38 |   - 无头模式: True
2025-07-17 23:23:44 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:24:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:24:36 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:24:36 | INFO     | src.api.browser_api:get_browser_manager:67 | 浏览器管理器初始化完成
2025-07-17 23:25:07 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://www.baidu.com
2025-07-17 23:25:45 | ERROR    | src.api.browser_api:browser_input:199 | 浏览器输入失败: name 'settings' is not defined
2025-07-17 23:29:11 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://www.baidu.com
2025-07-17 23:30:28 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:30:48 | INFO     | src.browser_controller.browser_manager:close_browser:245 | 浏览器已关闭
2025-07-17 23:30:48 | INFO     | main:lifespan:58 | 浏览器API实例已关闭
2025-07-17 23:30:48 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:30:59 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:30:59 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:30:59 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:30:59 | INFO     | main:lifespan:35 | 服务配置:
2025-07-17 23:30:59 | INFO     | main:lifespan:36 |   - API地址: 0.0.0.0:8001
2025-07-17 23:30:59 | INFO     | main:lifespan:37 |   - 浏览器类型: chrome
2025-07-17 23:30:59 | INFO     | main:lifespan:38 |   - 无头模式: False
2025-07-17 23:30:59 | INFO     | main:lifespan:39 |   - 调试模式: False
2025-07-17 23:34:03 | INFO     | main:lifespan:44 | 正在关闭Browser AI服务...
2025-07-17 23:34:03 | INFO     | main:lifespan:63 | Browser AI服务已关闭
2025-07-17 23:36:16 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:36:16 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:36:16 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:36:16 | INFO     | main:lifespan:37 | 服务配置:
2025-07-17 23:36:16 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-17 23:36:16 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-17 23:36:16 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-17 23:36:16 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-17 23:36:16 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-17 23:36:16 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:36:25 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:36:25 | INFO     | src.api.chat_api:get_browser_components:53 | 浏览器组件初始化完成
2025-07-17 23:36:25 | INFO     | src.api.chat_api:init_browser_on_startup:69 | 启动时浏览器初始化成功
2025-07-17 23:36:25 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-17 23:37:37 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:37:45 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:37:45 | INFO     | src.api.browser_api:get_browser_manager:67 | 浏览器管理器初始化完成
2025-07-17 23:38:15 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-17 23:38:35 | INFO     | src.browser_controller.browser_manager:close_browser:245 | 浏览器已关闭
2025-07-17 23:38:35 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-17 23:38:56 | INFO     | src.browser_controller.browser_manager:close_browser:245 | 浏览器已关闭
2025-07-17 23:38:56 | INFO     | main:lifespan:75 | 浏览器API实例已关闭
2025-07-17 23:38:56 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-17 23:39:10 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:39:10 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:39:10 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:39:10 | INFO     | main:lifespan:37 | 服务配置:
2025-07-17 23:39:10 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-17 23:39:10 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-17 23:39:10 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-17 23:39:10 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-17 23:39:10 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-17 23:39:10 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:39:16 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:39:16 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:39:16 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:39:16 | INFO     | main:lifespan:37 | 服务配置:
2025-07-17 23:39:16 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-17 23:39:16 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-17 23:39:16 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-17 23:39:16 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-17 23:39:16 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-17 23:39:16 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:39:24 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:39:24 | INFO     | src.api.chat_api:get_browser_components:53 | 浏览器组件初始化完成
2025-07-17 23:39:24 | INFO     | src.api.chat_api:init_browser_on_startup:69 | 启动时浏览器初始化成功
2025-07-17 23:39:24 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-17 23:39:24 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:39:24 | INFO     | src.api.chat_api:get_browser_components:53 | 浏览器组件初始化完成
2025-07-17 23:39:24 | INFO     | src.api.chat_api:init_browser_on_startup:69 | 启动时浏览器初始化成功
2025-07-17 23:39:24 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-17 23:39:24 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-17 23:39:30 | INFO     | src.browser_controller.browser_manager:close_browser:245 | 浏览器已关闭
2025-07-17 23:39:30 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-17 23:39:30 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-17 23:39:44 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:39:44 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:39:44 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:39:44 | INFO     | main:lifespan:37 | 服务配置:
2025-07-17 23:39:44 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8002
2025-07-17 23:39:44 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-17 23:39:44 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-17 23:39:44 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-17 23:39:44 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-17 23:39:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:39:54 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:39:54 | INFO     | src.api.chat_api:get_browser_components:53 | 浏览器组件初始化完成
2025-07-17 23:39:54 | INFO     | src.api.chat_api:init_browser_on_startup:69 | 启动时浏览器初始化成功
2025-07-17 23:39:54 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-17 23:40:46 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-17 23:41:07 | INFO     | src.browser_controller.browser_manager:close_browser:245 | 浏览器已关闭
2025-07-17 23:41:07 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-17 23:41:07 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-17 23:43:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:43:20 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:43:20 | INFO     | src.api.browser_api:get_browser_manager:67 | 浏览器管理器初始化完成
2025-07-17 23:43:21 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://www.baidu.com
2025-07-17 23:44:54 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://www.baidu.com
2025-07-17 23:57:16 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-17 23:57:16 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-17 23:57:16 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-17 23:57:16 | INFO     | main:lifespan:37 | 服务配置:
2025-07-17 23:57:16 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8002
2025-07-17 23:57:16 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-17 23:57:16 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-17 23:57:16 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-17 23:57:16 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-17 23:57:16 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:57:26 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:57:26 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-17 23:57:26 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-17 23:57:26 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://chat.openai.com
2025-07-17 23:57:39 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://chat.openai.com
2025-07-17 23:57:39 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-17 23:57:47 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-17 23:57:47 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-17 23:57:47 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-17 23:57:47 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-17 23:57:47 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-17 23:57:47 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-17 23:57:47 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-17 23:58:25 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
2025-07-17 23:58:36 | INFO     | src.browser_controller.browser_manager:start_browser:63 | 浏览器 chrome 启动成功
2025-07-17 23:58:36 | INFO     | src.api.browser_api:get_browser_manager:72 | 浏览器管理器初始化完成
2025-07-18 00:00:25 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://chat.openai.com
2025-07-18 00:00:42 | INFO     | src.browser_controller.browser_manager:navigate_to:174 | 导航到: https://chat.openai.com
2025-07-18 00:00:42 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 00:00:50 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 00:00:50 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 00:00:50 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 00:00:50 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 00:14:55 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 00:14:55 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 00:14:55 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 00:14:55 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 00:14:55 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 00:14:55 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 00:14:55 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 00:14:55 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 00:14:55 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 00:14:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:93 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\Application\chrome.exe
