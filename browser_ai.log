2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:35:46 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:35:46 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:35:46 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:35:46 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:35:46 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:35:46 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:37:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\78.0.3904.105\chromedriver.exe
2025-07-18 08:37:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:37:14 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:38:47 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\78.0.3904.105\chromedriver.exe
2025-07-18 08:38:47 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:38:49 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:39:32 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:39:32 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:39:32 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:39:32 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:39:32 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:39:32 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:43:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:43:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:44:12 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:45:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:45:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:46:47 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:46:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:46:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 08:48:02 | ERROR    | src.api.chat_api:init_browser_on_startup:86 | 启动时浏览器初始化失败: 
2025-07-18 08:48:02 | WARNING  | main:lifespan:52 | ⚠️ 浏览器启动时初始化失败，将在首次API调用时重试
2025-07-18 08:49:48 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 08:49:48 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 08:49:48 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:49:51 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:49:51 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:49:51 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:49:51 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:49:51 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:49:51 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:49:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:49:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:50:56 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:51:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:51:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-18 08:51:11 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:51:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:51:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
