2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:35:46 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:35:46 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:35:46 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:35:46 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:35:46 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:35:46 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:35:46 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:37:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\78.0.3904.105\chromedriver.exe
2025-07-18 08:37:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:37:14 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:37:14 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:37:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:38:47 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\78.0.3904.105\chromedriver.exe
2025-07-18 08:38:47 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:38:49 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Message: unknown error: no chrome binary at C:\Program Files\Google\Chrome\chrome.exe
Stacktrace:
Backtrace:
	Ordinal0 [0x0073A923+1550627]
	Ordinal0 [0x006BA731+1025841]
	Ordinal0 [0x0063C715+509717]
	Ordinal0 [0x005CBB8C+48012]
	Ordinal0 [0x005EACE7+175335]
	Ordinal0 [0x005EA8ED+174317]
	Ordinal0 [0x005E8CDB+167131]
	Ordinal0 [0x005D144A+70730]
	Ordinal0 [0x005D24D0+74960]
	Ordinal0 [0x005D2469+74857]
	Ordinal0 [0x006D42F7+1131255]
	GetHandleVerifier [0x007D711D+523789]
	GetHandleVerifier [0x007D6EB0+523168]
	GetHandleVerifier [0x007DE207+552695]
	GetHandleVerifier [0x007D791A+525834]
	Ordinal0 [0x006CB82C+1095724]
	Ordinal0 [0x006D636B+1139563]
	Ordinal0 [0x006D64D3+1139923]
	Ordinal0 [0x006D5455+1135701]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:38:49 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\chrome.exe
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:38:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:39:32 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:39:32 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:39:32 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:39:32 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:39:32 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:39:32 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:39:32 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:39:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:43:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:43:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:44:12 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:44:12 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:44:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:45:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:45:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:46:47 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:46:47 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:46:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:46:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:46:55 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:47:58 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 08:48:02 | ERROR    | src.api.chat_api:init_browser_on_startup:86 | 启动时浏览器初始化失败: 
2025-07-18 08:48:02 | WARNING  | main:lifespan:52 | ⚠️ 浏览器启动时初始化失败，将在首次API调用时重试
2025-07-18 08:49:48 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 08:49:48 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 08:49:48 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:49:51 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:49:51 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:49:51 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 08:49:51 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:49:51 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 08:49:51 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:49:51 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:49:51 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:49:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:49:53 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:50:56 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: DevToolsActivePort file doesn't exist
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00461D8E]
	(No symbol) [0x0045D3F0]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:50:56 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:51:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:51:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:51:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | Chrome浏览器启动失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | Windows系统浏览器启动失败，可能的原因：
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:203 | 1. Chrome进程被防火墙阻止
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:204 | 2. Chrome版本与ChromeDriver不兼容
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:205 | 3. 系统资源不足
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:_start_chrome:206 | 4. Chrome安装路径问题
2025-07-18 08:51:11 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-18 08:51:11 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:125 | 添加了 33 个稳定性参数
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:130 | 添加了 7 个配置参数
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:140 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:151 | 正在准备ChromeDriver...
2025-07-18 08:51:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:158 | 正在下载/检查ChromeDriver...
2025-07-18 08:51:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:162 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:51:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | 正在启动Chrome浏览器...
2025-07-18 08:58:38 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 08:58:38 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 08:58:38 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 08:58:38 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 08:58:38 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8002
2025-07-18 08:58:38 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 08:58:38 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-18 08:58:38 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 08:58:38 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:131 | 启用无头模式
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 08:58:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 08:58:40 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 08:58:40 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 08:58:40 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 08:58:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 08:58:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 08:58:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 08:58:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 08:58:41 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 08:58:41 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 08:58:41 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 08:58:41 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://chat.openai.com
2025-07-18 08:59:11 | ERROR    | src.browser_controller.browser_manager:navigate_to:285 | 导航失败: Message: timeout: Timed out receiving message from renderer: -0.014
  (Session info: chrome=109.0.5414.168)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0042F55A]
	(No symbol) [0x0042F2D8]
	(No symbol) [0x0042DC68]
	(No symbol) [0x0042E647]
	(No symbol) [0x00438568]
	(No symbol) [0x00444956]
	(No symbol) [0x004481C6]
	(No symbol) [0x0042E9F1]
	(No symbol) [0x004446D5]
	(No symbol) [0x004A69B5]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 08:59:11 | ERROR    | src.browser_controller.ai_website_manager:navigate_to_ai_website:108 | 导航到 ChatGPT 失败
2025-07-18 08:59:11 | WARNING  | src.api.chat_api:get_browser_components:67 | ⚠️ AI网站导航失败，将使用默认配置
2025-07-18 08:59:11 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 08:59:11 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:131 | 启用无头模式
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 08:59:58 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:00:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:00:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:00:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:193 | Chrome浏览器启动失败: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:194 | 错误类型: WebDriverException
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:197 | 可能的解决方案：
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:198 | 1. 检查Chrome浏览器是否正确安装
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | 2. 确保Chrome路径配置正确
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:200 | 3. 尝试重启计算机
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:201 | 4. 检查防火墙和杀毒软件设置
2025-07-18 09:00:04 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | 5. 确保系统有足够的内存和磁盘空间
2025-07-18 09:00:05 | INFO     | src.browser_controller.browser_manager:_start_chrome:211 | 已尝试清理Chrome进程
2025-07-18 09:00:05 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:00:05 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:131 | 启用无头模式
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:00:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:00:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:00:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:00:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:00:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:00:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:00:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:00:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:00:15 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:00:15 | INFO     | src.api.browser_api:get_browser_manager:72 | 浏览器管理器初始化完成
2025-07-18 09:00:34 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://www.baidu.com
2025-07-18 09:02:08 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://www.baidu.com
2025-07-18 09:16:12 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:16:33 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:16:33 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:16:53 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:16:53 | INFO     | main:lifespan:75 | 浏览器API实例已关闭
2025-07-18 09:16:53 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:20:06 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:20:06 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:20:06 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:20:06 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:20:06 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 09:20:06 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:20:06 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:20:06 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:20:06 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:20:06 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:20:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:20:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:20:09 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:193 | Chrome浏览器启动失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:194 | 错误类型: WebDriverException
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:197 | 可能的解决方案：
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:198 | 1. 检查Chrome浏览器是否正确安装
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | 2. 确保Chrome路径配置正确
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:200 | 3. 尝试重启计算机
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:201 | 4. 检查防火墙和杀毒软件设置
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | 5. 确保系统有足够的内存和磁盘空间
2025-07-18 09:20:12 | INFO     | src.browser_controller.browser_manager:_start_chrome:211 | 已尝试清理Chrome进程
2025-07-18 09:20:12 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:20:12 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:20:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:20:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:20:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:20:21 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:20:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:20:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:20:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:20:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:20:22 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:20:22 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:20:22 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:20:22 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://chat.openai.com
2025-07-18 09:20:25 | ERROR    | src.browser_controller.browser_manager:navigate_to:285 | 导航失败: Message: unknown error: net::ERR_CONNECTION_CLOSED
  (Session info: chrome=109.0.5414.168)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x00439E22]
	(No symbol) [0x0042FCFD]
	(No symbol) [0x00431101]
	(No symbol) [0x0042FFDD]
	(No symbol) [0x0042F3BC]
	(No symbol) [0x0042F2D8]
	(No symbol) [0x0042DC68]
	(No symbol) [0x0042E512]
	(No symbol) [0x0043F75B]
	(No symbol) [0x004A7727]
	(No symbol) [0x0048FD7C]
	(No symbol) [0x004A6B09]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:20:25 | ERROR    | src.browser_controller.ai_website_manager:navigate_to_ai_website:108 | 导航到 ChatGPT 失败
2025-07-18 09:20:25 | WARNING  | src.api.chat_api:get_browser_components:67 | ⚠️ AI网站导航失败，将使用默认配置
2025-07-18 09:20:25 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:20:25 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:21:01 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:21:01 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:21:01 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:21:01 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:21:01 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 09:21:01 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:21:01 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:21:01 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:21:01 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:21:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:21:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:21:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:21:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:21:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:21:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:21:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:21:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:21:05 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:21:05 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:21:05 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:21:05 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://freegpt.be
2025-07-18 09:21:35 | ERROR    | src.browser_controller.browser_manager:navigate_to:285 | 导航失败: Message: timeout: Timed out receiving message from renderer: 28.404
  (Session info: chrome=109.0.5414.168)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0042F55A]
	(No symbol) [0x0042F2D8]
	(No symbol) [0x0042DC68]
	(No symbol) [0x0042E647]
	(No symbol) [0x00438568]
	(No symbol) [0x00444956]
	(No symbol) [0x004481C6]
	(No symbol) [0x0042E9F1]
	(No symbol) [0x004446D5]
	(No symbol) [0x004A7057]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:21:35 | ERROR    | src.browser_controller.ai_website_manager:navigate_to_ai_website:108 | 导航到 ChatGPT 失败
2025-07-18 09:21:35 | WARNING  | src.api.chat_api:get_browser_components:67 | ⚠️ AI网站导航失败，将使用默认配置
2025-07-18 09:21:35 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:21:35 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:28:14 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:28:35 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:28:35 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:28:35 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:29:20 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:29:20 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:29:20 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:29:20 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:29:20 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 09:29:20 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:29:20 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:29:20 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:29:20 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:29:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:29:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:29:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:29:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:29:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:29:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:29:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:29:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:29:23 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:29:23 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:29:23 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:29:23 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://freegpt.be
2025-07-18 09:29:25 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://freegpt.be
2025-07-18 09:29:25 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 09:29:33 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 09:29:33 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:124 | ✅ ChatGPT 页面验证成功
2025-07-18 09:29:33 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 09:29:33 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 09:29:33 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 09:29:33 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:29:33 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:33:49 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:34:14 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:34:14 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:34:14 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:35:43 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:35:43 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:35:43 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:35:43 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:35:43 | INFO     | main:lifespan:38 |   - API地址: 0.0.0.0:8001
2025-07-18 09:35:43 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:35:43 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:35:43 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:35:43 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:35:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:35:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:35:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:35:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:35:46 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:35:46 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:35:46 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:35:46 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:35:46 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:35:47 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:35:47 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 09:35:55 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 09:35:55 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 09:35:55 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 09:35:55 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 09:35:55 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 09:35:55 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:35:55 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:42:31 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:42:56 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:42:56 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:42:56 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:49:04 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:49:04 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:49:04 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:49:04 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:49:04 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 09:49:04 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:49:04 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:49:04 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:49:04 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:49:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:49:06 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:49:06 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:49:06 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:49:08 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:49:09 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:49:09 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:49:09 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:49:11 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:49:56 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:49:56 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:49:56 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 09:49:56 | ERROR    | src.api.chat_api:init_browser_on_startup:86 | 启动时浏览器初始化失败: 
2025-07-18 09:49:56 | WARNING  | main:lifespan:52 | ⚠️ 浏览器启动时初始化失败，将在首次API调用时重试
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:50:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:50:34 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:50:34 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:50:34 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:50:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:50:37 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:50:37 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:50:37 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:50:39 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:50:41 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:50:41 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:50:41 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 09:50:41 | ERROR    | src.api.browser_api:get_available_ai_websites:431 | 获取可用AI网站失败: 
2025-07-18 09:51:54 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:51:54 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:51:54 | INFO     | main:lifespan:75 | 浏览器API实例已关闭
2025-07-18 09:51:54 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:51:57 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:51:57 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:51:57 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:51:57 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:51:57 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 09:51:57 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:51:57 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:51:57 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:51:57 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:51:57 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:52:42 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:52:42 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:52:42 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:52:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:53:29 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:53:29 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:53:29 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:53:31 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:53:32 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:53:32 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:53:32 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 09:53:32 | ERROR    | src.api.chat_api:init_browser_on_startup:86 | 启动时浏览器初始化失败: 
2025-07-18 09:53:32 | WARNING  | main:lifespan:52 | ⚠️ 浏览器启动时初始化失败，将在首次API调用时重试
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:53:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:54:17 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:54:17 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:54:17 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:54:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:54:21 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:54:21 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 2 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:54:21 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:54:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:54:23 | ERROR    | src.browser_controller.browser_manager:_start_chrome:163 | ChromeDriver下载失败: Could not reach host. Are you offline?
2025-07-18 09:54:23 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 3 次启动浏览器失败: Could not reach host. Are you offline?
2025-07-18 09:54:23 | ERROR    | src.browser_controller.browser_manager:start_browser:74 | 经过 3 次尝试后仍然失败
2025-07-18 09:54:23 | ERROR    | src.api.browser_api:get_ai_website_status:329 | 获取AI网站状态失败: 
2025-07-18 09:55:26 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:55:26 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:55:26 | INFO     | main:lifespan:75 | 浏览器API实例已关闭
2025-07-18 09:55:26 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:55:29 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:55:29 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:55:29 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:55:29 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:55:29 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 09:55:29 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:55:29 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:55:29 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:55:29 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:55:29 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:55:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:55:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:55:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:55:33 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:55:33 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:55:33 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:55:33 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:55:33 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:55:33 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:55:33 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:55:33 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:55:34 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:55:34 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 09:55:42 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 09:55:42 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 09:55:42 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 09:55:42 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 09:55:42 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 09:55:42 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:55:42 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:55:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:55:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:55:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:55:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:193 | Chrome浏览器启动失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:194 | 错误类型: WebDriverException
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:197 | 可能的解决方案：
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:198 | 1. 检查Chrome浏览器是否正确安装
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | 2. 确保Chrome路径配置正确
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:200 | 3. 尝试重启计算机
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:201 | 4. 检查防火墙和杀毒软件设置
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | 5. 确保系统有足够的内存和磁盘空间
2025-07-18 09:55:48 | INFO     | src.browser_controller.browser_manager:_start_chrome:211 | 已尝试清理Chrome进程
2025-07-18 09:55:48 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:55:48 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:55:54 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:55:56 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:55:56 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:55:56 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:56:01 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:56:01 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:56:01 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:56:01 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:56:01 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 09:56:01 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:56:01 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:56:01 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:56:01 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:56:01 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:56:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:56:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:56:03 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:56:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:56:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:56:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:56:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:56:04 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:56:04 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:56:04 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:56:04 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:56:05 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:56:05 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 09:56:13 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 09:56:13 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 09:56:13 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 09:56:13 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 09:56:13 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 09:56:13 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:56:13 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:56:13 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:56:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:56:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:56:15 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:193 | Chrome浏览器启动失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:194 | 错误类型: WebDriverException
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:197 | 可能的解决方案：
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:198 | 1. 检查Chrome浏览器是否正确安装
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | 2. 确保Chrome路径配置正确
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:200 | 3. 尝试重启计算机
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:201 | 4. 检查防火墙和杀毒软件设置
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | 5. 确保系统有足够的内存和磁盘空间
2025-07-18 09:56:18 | INFO     | src.browser_controller.browser_manager:_start_chrome:211 | 已尝试清理Chrome进程
2025-07-18 09:56:18 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:56:18 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:56:24 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:56:26 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:56:26 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:56:26 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:56:27 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:56:27 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:56:27 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:56:27 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:56:28 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:56:28 | INFO     | src.api.browser_api:get_browser_manager:72 | 浏览器管理器初始化完成
2025-07-18 09:56:28 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 09:56:48 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:56:48 | INFO     | main:lifespan:71 | 聊天API浏览器实例已关闭
2025-07-18 09:57:12 | INFO     | src.browser_controller.browser_manager:close_browser:352 | 浏览器已关闭
2025-07-18 09:57:12 | INFO     | main:lifespan:75 | 浏览器API实例已关闭
2025-07-18 09:57:12 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 09:57:19 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 09:57:19 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 09:57:19 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 09:57:19 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 09:57:19 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 09:57:19 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 09:57:19 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 09:57:19 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 09:57:19 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:57:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:57:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:57:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:57:22 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:57:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 09:57:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 09:57:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 09:57:23 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 09:57:23 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 09:57:23 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 09:57:23 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 09:57:23 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:57:25 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 09:57:25 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 09:57:33 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 09:57:33 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 09:57:33 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 09:57:33 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 09:57:33 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 09:57:33 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 09:57:33 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 09:57:42 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 09:57:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 09:57:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 09:57:45 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:193 | Chrome浏览器启动失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:194 | 错误类型: WebDriverException
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:197 | 可能的解决方案：
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:198 | 1. 检查Chrome浏览器是否正确安装
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:199 | 2. 确保Chrome路径配置正确
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:200 | 3. 尝试重启计算机
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:201 | 4. 检查防火墙和杀毒软件设置
2025-07-18 09:57:47 | ERROR    | src.browser_controller.browser_manager:_start_chrome:202 | 5. 确保系统有足够的内存和磁盘空间
2025-07-18 09:57:48 | INFO     | src.browser_controller.browser_manager:_start_chrome:211 | 已尝试清理Chrome进程
2025-07-18 09:57:48 | ERROR    | src.browser_controller.browser_manager:start_browser:68 | 第 1 次启动浏览器失败: Message: unknown error: Chrome failed to start: exited normally.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Program Files\Google\Chrome\App\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	(No symbol) [0x005A6643]
	(No symbol) [0x0053BE21]
	(No symbol) [0x0043DA9D]
	(No symbol) [0x0045F2A7]
	(No symbol) [0x0045A899]
	(No symbol) [0x00496917]
	(No symbol) [0x0049655C]
	(No symbol) [0x0048FB76]
	(No symbol) [0x004649C1]
	(No symbol) [0x00465E5D]
	GetHandleVerifier [0x0081A142+2497106]
	GetHandleVerifier [0x008485D3+2686691]
	GetHandleVerifier [0x0084BB9C+2700460]
	GetHandleVerifier [0x00653B10+635936]
	(No symbol) [0x00544A1F]
	(No symbol) [0x0054A418]
	(No symbol) [0x0054A505]
	(No symbol) [0x0055508B]
	BaseThreadInitThunk [0x76ABFCC9+25]
	RtlGetAppContainerNamedObjectPath [0x778382AE+286]
	RtlGetAppContainerNamedObjectPath [0x7783827E+238]

2025-07-18 09:57:48 | INFO     | src.browser_controller.browser_manager:start_browser:71 | 2秒后重试...
2025-07-18 10:01:41 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 10:01:41 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 10:01:41 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 10:01:41 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 10:01:41 | INFO     | main:lifespan:38 |   - API地址: localhost:8002
2025-07-18 10:01:41 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 10:01:41 | INFO     | main:lifespan:40 |   - 无头模式: True
2025-07-18 10:01:41 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 10:01:41 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:131 | 启用无头模式
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 10:01:41 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 10:01:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 10:01:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 10:01:43 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 10:01:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 10:01:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 10:01:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 10:01:44 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 10:01:44 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 10:01:44 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 10:01:44 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 10:01:44 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:01:45 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:01:45 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 10:01:53 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 10:01:53 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 10:01:53 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 10:01:53 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 10:01:53 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 10:01:53 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 10:01:53 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 10:04:02 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 10:04:02 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 10:04:02 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 10:04:02 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 10:04:02 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 10:04:02 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 10:04:02 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 10:04:02 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 10:04:02 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 10:04:02 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 10:04:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 10:04:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 10:04:04 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 10:04:05 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 10:04:05 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 10:04:05 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 10:04:05 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 10:04:06 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 10:04:06 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 10:04:06 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 10:04:06 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:04:07 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:04:07 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 10:04:15 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 10:04:15 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 10:04:15 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 10:04:15 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 10:04:15 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 10:04:15 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 10:04:15 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 10:04:27 | INFO     | src.api.chat_api:create_chat_completion:131 | 处理用户消息: 你好...
2025-07-18 10:05:17 | WARNING  | src.browser_controller.input_handler:find_input_element:71 | 未找到可用的输入框
2025-07-18 10:05:18 | WARNING  | src.browser_controller.input_handler:wait_for_input_ready:236 | 等待输入框准备就绪超时 (10秒)
2025-07-18 10:06:52 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 10:06:52 | ERROR    | main:lifespan:78 | 清理浏览器资源时发生错误: cannot import name 'browser_manager' from 'src.api.browser_api' (D:\app\browerAi\src\api\browser_api.py)
2025-07-18 10:06:52 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 10:28:30 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 10:28:30 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 10:28:30 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 10:28:30 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 10:28:30 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 10:28:30 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 10:28:30 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 10:28:30 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 10:28:30 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 10:28:30 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 10:28:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 10:28:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 10:28:32 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 10:28:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 10:28:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 10:28:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 10:28:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 10:28:34 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 10:28:34 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 10:28:34 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 10:28:34 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:28:36 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 10:28:36 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 10:28:44 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 10:28:44 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 10:28:44 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 10:28:44 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 10:28:44 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 10:28:44 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 10:28:44 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 10:29:17 | INFO     | src.api.chat_api:create_chat_completion:131 | 处理用户消息: 你好啊...
2025-07-18 10:30:37 | WARNING  | src.browser_controller.input_handler:find_input_element:168 | 未找到可用的输入框
2025-07-18 10:30:38 | WARNING  | src.browser_controller.input_handler:wait_for_input_ready:536 | 等待输入框准备就绪超时 (10秒，尝试 1 次)
2025-07-18 10:31:46 | INFO     | main:lifespan:61 | 正在关闭Browser AI服务...
2025-07-18 10:31:46 | ERROR    | main:lifespan:78 | 清理浏览器资源时发生错误: cannot import name 'browser_manager' from 'src.api.browser_api' (D:\app\browerAi\src\api\browser_api.py)
2025-07-18 10:31:46 | INFO     | main:lifespan:80 | Browser AI服务已关闭
2025-07-18 11:08:17 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 11:08:17 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 11:08:17 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 11:08:17 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 11:08:17 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 11:08:17 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 11:08:17 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 11:08:17 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 11:08:17 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 11:08:17 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 11:08:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 11:08:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 11:08:19 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 11:08:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 11:08:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 11:08:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 11:08:20 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 11:08:20 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 11:08:20 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 11:08:20 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 11:08:20 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 11:08:22 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 11:08:22 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 11:08:30 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 11:08:30 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 11:08:30 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 11:08:30 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 11:08:30 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 11:08:30 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 11:08:30 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 11:10:24 | INFO     | src.api.chat_api:create_chat_completion:131 | 处理用户消息: 你哈...
2025-07-18 11:18:34 | INFO     | src.utils.logger_config:setup_logger:59 | 日志系统初始化完成
2025-07-18 11:18:34 | INFO     | src.utils.logger_config:setup_logger:60 | 日志级别: INFO
2025-07-18 11:18:34 | INFO     | src.utils.logger_config:setup_logger:61 | 日志文件: browser_ai.log
2025-07-18 11:18:34 | INFO     | main:lifespan:37 | 服务配置:
2025-07-18 11:18:34 | INFO     | main:lifespan:38 |   - API地址: localhost:8001
2025-07-18 11:18:34 | INFO     | main:lifespan:39 |   - 浏览器类型: chrome
2025-07-18 11:18:34 | INFO     | main:lifespan:40 |   - 无头模式: False
2025-07-18 11:18:34 | INFO     | main:lifespan:41 |   - 调试模式: False
2025-07-18 11:18:34 | INFO     | main:lifespan:44 | 正在初始化浏览器...
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:81 | 开始配置Chrome选项...
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:121 | 添加了 29 个稳定性参数
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:126 | 添加了 7 个配置参数
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:136 | 使用指定的Chrome路径: C:\Program Files\Google\Chrome\App\chrome.exe
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:150 | 正在准备ChromeDriver...
2025-07-18 11:18:34 | INFO     | src.browser_controller.browser_manager:_start_chrome:157 | 正在下载/检查ChromeDriver...
2025-07-18 11:18:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:161 | ChromeDriver路径: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\109.0.5414.74\chromedriver.exe
2025-07-18 11:18:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:167 | 正在启动Chrome浏览器...
2025-07-18 11:18:36 | INFO     | src.browser_controller.browser_manager:_start_chrome:168 | ⏳ 这可能需要一些时间，请耐心等待...
2025-07-18 11:18:37 | INFO     | src.browser_controller.browser_manager:_start_chrome:180 | ✅ Chrome浏览器启动成功
2025-07-18 11:18:37 | INFO     | src.browser_controller.browser_manager:_start_chrome:185 | 浏览器超时设置完成
2025-07-18 11:18:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:188 | 正在测试浏览器功能...
2025-07-18 11:18:38 | INFO     | src.browser_controller.browser_manager:_start_chrome:190 | ✅ 浏览器功能测试通过
2025-07-18 11:18:38 | INFO     | src.browser_controller.browser_manager:start_browser:64 | 浏览器 chrome 启动成功
2025-07-18 11:18:38 | INFO     | src.api.chat_api:get_browser_components:56 | 浏览器组件初始化完成
2025-07-18 11:18:38 | INFO     | src.api.chat_api:get_browser_components:60 | 正在自动导航到AI网站...
2025-07-18 11:18:38 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:104 | 正在导航到 ChatGPT: https://space.coze.cn/task/7528225125881594131
2025-07-18 11:18:39 | INFO     | src.browser_controller.browser_manager:navigate_to:281 | 导航到: https://space.coze.cn/task/7528225125881594131
2025-07-18 11:18:39 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:113 | 等待页面加载完成 (8秒)...
2025-07-18 11:18:47 | INFO     | src.browser_controller.ai_website_manager:navigate_to_ai_website:120 | ✅ 成功导航到 ChatGPT
2025-07-18 11:18:47 | WARNING  | src.browser_controller.ai_website_manager:navigate_to_ai_website:127 | ⚠️ ChatGPT 页面验证失败，但导航已完成
2025-07-18 11:18:47 | INFO     | src.browser_controller.ai_website_manager:update_input_handler_selectors:208 | 已更新输入选择器为 chatgpt 配置
2025-07-18 11:18:47 | INFO     | src.browser_controller.ai_website_manager:update_response_monitor_selectors:234 | 已更新响应选择器为 chatgpt 配置
2025-07-18 11:18:47 | INFO     | src.api.chat_api:get_browser_components:65 | ✅ AI网站导航和配置完成
2025-07-18 11:18:47 | INFO     | src.api.chat_api:init_browser_on_startup:83 | 启动时浏览器初始化成功
2025-07-18 11:18:47 | INFO     | main:lifespan:50 | ✅ 浏览器在启动时初始化成功
2025-07-18 11:19:00 | INFO     | src.api.chat_api:create_chat_completion:131 | 处理用户消息: 你好...
