"""
测试单一浏览器实例 - 验证不会重复创建浏览器
"""

import requests
import time
import psutil
import subprocess
from typing import List


def get_chrome_processes() -> List[dict]:
    """获取当前运行的Chrome进程"""
    chrome_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return chrome_processes


def count_browser_ai_chrome_processes() -> int:
    """统计Browser AI相关的Chrome进程数量"""
    count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'chrome_browser_ai' in cmdline or 'chromedriver' in cmdline:
                    count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return count


def test_multiple_api_calls():
    """测试多次API调用是否会创建多个浏览器实例"""
    base_url = "http://localhost:8002"
    
    print("🧪 测试单一浏览器实例")
    print("=" * 50)
    
    # 清理之前的Chrome进程
    print("🧹 清理之前的Chrome进程...")
    try:
        subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"], 
                      capture_output=True, timeout=5)
        subprocess.run(["taskkill", "/F", "/IM", "chromedriver.exe"], 
                      capture_output=True, timeout=5)
        time.sleep(2)
    except:
        pass
    
    # 记录初始Chrome进程数
    initial_count = count_browser_ai_chrome_processes()
    print(f"初始Browser AI Chrome进程数: {initial_count}")
    
    # 测试多次API调用
    api_calls = [
        ("健康检查", "GET", "/health"),
        ("浏览器状态", "GET", "/browser/status"),
        ("AI网站状态", "GET", "/ai-website/status"),
        ("可用AI网站", "GET", "/ai-website/available"),
        ("导航测试", "POST", "/browser/navigate", {"url": "https://www.baidu.com", "wait_for_load": True}),
        ("页面源码", "GET", "/browser/page-source"),
        ("浏览器状态2", "GET", "/browser/status"),
    ]
    
    print(f"\n📊 执行 {len(api_calls)} 个API调用...")
    
    for i, call_info in enumerate(api_calls, 1):
        name = call_info[0]
        method = call_info[1]
        endpoint = call_info[2]
        data = call_info[3] if len(call_info) > 3 else None
        
        print(f"\n{i}. {name} ({method} {endpoint})")
        
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=30)
            else:
                response = requests.post(f"{base_url}{endpoint}", json=data, timeout=60)
            
            if response.status_code == 200:
                print(f"   ✅ 成功 (状态码: {response.status_code})")
            else:
                print(f"   ⚠️ 响应异常 (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
        
        # 检查Chrome进程数
        current_count = count_browser_ai_chrome_processes()
        print(f"   🔍 当前Browser AI Chrome进程数: {current_count}")
        
        # 短暂等待
        time.sleep(1)
    
    # 最终检查
    final_count = count_browser_ai_chrome_processes()
    print(f"\n📈 进程数变化:")
    print(f"   初始: {initial_count}")
    print(f"   最终: {final_count}")
    print(f"   增加: {final_count - initial_count}")
    
    # 分析结果
    if final_count - initial_count <= 5:  # 允许一些合理的子进程
        print(f"\n✅ 测试通过！浏览器实例控制良好")
        print(f"   进程增加数量在合理范围内 ({final_count - initial_count} 个)")
    else:
        print(f"\n❌ 测试失败！可能存在浏览器实例重复创建")
        print(f"   进程增加过多 ({final_count - initial_count} 个)")
    
    # 显示详细的Chrome进程信息
    print(f"\n🔍 当前Chrome进程详情:")
    chrome_procs = get_chrome_processes()
    browser_ai_procs = [p for p in chrome_procs if 'chrome_browser_ai' in p['cmdline'] or 'chromedriver' in p['cmdline']]
    
    if browser_ai_procs:
        for proc in browser_ai_procs:
            print(f"   PID: {proc['pid']}, 名称: {proc['name']}")
            print(f"   命令行: {proc['cmdline'][:100]}...")
    else:
        print("   未找到Browser AI相关的Chrome进程")
    
    return final_count - initial_count <= 5


def main():
    """主函数"""
    try:
        print("🚀 Browser AI 单一浏览器实例测试")
        print("=" * 60)
        
        # 检查服务是否运行
        try:
            response = requests.get("http://localhost:8002/health", timeout=5)
            if response.status_code == 200:
                print("✅ Browser AI 服务正在运行")
            else:
                print("❌ Browser AI 服务响应异常")
                return
        except:
            print("❌ Browser AI 服务未运行，请先启动服务:")
            print("   python start.py --port 8002 --headless")
            return
        
        # 运行测试
        success = test_multiple_api_calls()
        
        if success:
            print("\n🎉 单一浏览器实例测试通过！")
            print("\n💡 修复效果:")
            print("✅ 多次API调用不会重复创建浏览器")
            print("✅ 浏览器管理器正确实现单例模式")
            print("✅ 内存和进程使用优化")
        else:
            print("\n⚠️ 测试未完全通过，可能仍存在重复创建问题")
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
