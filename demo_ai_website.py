"""
AI网站导航功能演示脚本
展示如何自动导航到AI对话网站并配置选择器
"""

import requests
import time
import json
import webbrowser
from typing import Dict, Any, List


class AIWebsiteDemo:
    """AI网站导航演示类"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        """
        初始化演示类
        参数: base_url - API服务地址
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = 30
        
    def check_service_status(self) -> bool:
        """检查服务状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务状态: {data['status']}")
                return True
            else:
                print(f"❌ 服务状态异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False
    
    def get_ai_website_status(self) -> Dict[str, Any]:
        """获取AI网站状态"""
        try:
            response = requests.get(f"{self.base_url}/ai-website/status", timeout=30)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI网站状态:")
                print(f"   当前网站: {data.get('current_website', 'None')}")
                print(f"   当前URL: {data.get('current_url', 'None')}")
                print(f"   默认网站: {data.get('default_website', 'None')}")
                print(f"   自动导航: {data.get('auto_navigate', False)}")
                return data
            else:
                print(f"❌ AI网站状态获取失败: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ AI网站状态获取异常: {e}")
            return {}
    
    def get_available_websites(self) -> List[Dict[str, Any]]:
        """获取可用的AI网站列表"""
        try:
            response = requests.get(f"{self.base_url}/ai-website/available", timeout=30)
            if response.status_code == 200:
                data = response.json()
                websites = data.get('websites', [])
                print(f"✅ 可用AI网站 ({len(websites)}个):")
                for site in websites:
                    current_mark = " ⭐" if site.get('current') else ""
                    print(f"   • {site['name']} ({site['key']}){current_mark}")
                    print(f"     URL: {site['url']}")
                return websites
            else:
                print(f"❌ 获取可用网站失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取可用网站异常: {e}")
            return []
    
    def navigate_to_website(self, website: str, auto_configure: bool = True) -> bool:
        """导航到指定AI网站"""
        try:
            print(f"🌐 正在导航到 {website}...")
            data = {
                "website": website,
                "auto_configure": auto_configure
            }
            response = requests.post(
                f"{self.base_url}/ai-website/navigate",
                json=data,
                timeout=120  # 导航可能需要较长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 导航成功: {result['message']}")
                    print(f"   网站: {result['website']}")
                    print(f"   URL: {result['url']}")
                    if result.get('auto_configured'):
                        print(f"   ✅ 选择器已自动配置")
                    else:
                        print(f"   ⚠️ 选择器配置失败")
                    return True
                else:
                    print(f"❌ 导航失败: {result}")
                    return False
            else:
                print(f"❌ 导航请求失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data.get('detail', 'Unknown error')}")
                except:
                    pass
                return False
        except Exception as e:
            print(f"❌ 导航异常: {e}")
            return False
    
    def test_chat_after_navigation(self, message: str = "Hello! Can you help me test this interface?") -> bool:
        """导航后测试聊天功能"""
        try:
            print(f"💬 测试聊天功能...")
            print(f"   发送消息: {message}")
            
            data = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": message}],
                "temperature": 0.7,
                "max_tokens": 500
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result["choices"][0]["message"]["content"]
                print(f"✅ 聊天测试成功")
                print(f"   AI回复: {ai_response[:150]}...")
                return True
            else:
                print(f"❌ 聊天测试失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data.get('detail', 'Unknown error')}")
                except:
                    pass
                return False
                
        except Exception as e:
            print(f"❌ 聊天测试异常: {e}")
            return False
    
    def run_demo(self):
        """运行完整演示"""
        print("🚀 AI网站导航功能演示")
        print("=" * 60)
        
        # 1. 检查服务状态
        print("\n1. 检查服务状态...")
        if not self.check_service_status():
            print("❌ 服务未启动，请先运行: python start.py --port 8002")
            return False
        
        # 2. 获取当前AI网站状态
        print("\n2. 获取当前AI网站状态...")
        current_status = self.get_ai_website_status()
        
        # 3. 获取可用AI网站列表
        print("\n3. 获取可用AI网站列表...")
        available_websites = self.get_available_websites()
        
        if not available_websites:
            print("❌ 没有可用的AI网站")
            return False
        
        # 4. 演示导航功能
        print("\n4. 演示AI网站导航功能...")
        
        # 如果当前没有导航到任何网站，或者想演示切换
        current_website = current_status.get('current_website')
        
        # 选择一个不同的网站进行演示
        demo_websites = ['chatgpt', 'claude', 'bard']
        target_website = None
        
        for website in demo_websites:
            if website != current_website:
                # 检查这个网站是否可用
                for site in available_websites:
                    if site['key'] == website:
                        target_website = website
                        break
                if target_website:
                    break
        
        if not target_website:
            target_website = available_websites[0]['key']
        
        print(f"   选择演示网站: {target_website}")
        
        # 导航到目标网站
        if self.navigate_to_website(target_website):
            # 5. 再次检查状态
            print("\n5. 导航后检查状态...")
            self.get_ai_website_status()
            
            # 6. 测试聊天功能（可选）
            print("\n6. 测试聊天功能...")
            print("   注意：实际聊天功能需要AI网站完全加载并可交互")
            print("   当前演示仅测试API调用，实际效果取决于目标网站状态")
            
            # 可以选择是否测试聊天
            try:
                test_chat = input("\n是否测试聊天功能？(y/N): ").lower().strip()
                if test_chat == 'y':
                    self.test_chat_after_navigation()
                else:
                    print("   跳过聊天测试")
            except:
                print("   跳过聊天测试")
        
        # 7. 打开网页界面
        print("\n7. 打开网页界面...")
        try:
            chat_url = f"{self.base_url}/chat"
            print(f"   网页界面: {chat_url}")
            webbrowser.open(chat_url)
            print("✅ 网页界面已在浏览器中打开")
        except Exception as e:
            print(f"❌ 打开网页界面失败: {e}")
        
        # 8. 总结
        print("\n" + "=" * 60)
        print("🎉 AI网站导航功能演示完成！")
        
        print("\n📋 演示总结:")
        print("✅ 服务状态检查 - 确认服务正常运行")
        print("✅ AI网站状态获取 - 查看当前导航状态")
        print("✅ 可用网站列表 - 显示所有配置的AI网站")
        print("✅ 网站导航功能 - 自动导航到指定AI网站")
        print("✅ 选择器自动配置 - 根据网站类型配置输入和响应选择器")
        print("✅ 网页界面集成 - 在Web界面中管理AI网站")
        
        print("\n💡 关键特性:")
        print("• 🌐 支持多个主流AI网站 (ChatGPT, Claude, Bard)")
        print("• ⚙️ 自动配置选择器以匹配不同网站")
        print("• 🚀 启动时自动导航到默认AI网站")
        print("• 🔄 支持运行时切换不同AI网站")
        print("• 📱 Web界面提供友好的操作体验")
        
        print("\n🔧 配置说明:")
        print("• 在 .env 文件中配置默认AI网站和URL")
        print("• 可以自定义输入框和响应选择器")
        print("• 支持添加自定义AI网站配置")
        
        return True


def main():
    """主函数"""
    try:
        # 创建演示实例
        demo = AIWebsiteDemo("http://localhost:8002")
        
        # 运行演示
        success = demo.run_demo()
        
        if success:
            print("\n🎊 演示完成！现在您可以：")
            print("1. 在网页界面中选择不同的AI网站")
            print("2. 点击'导航'按钮切换到其他AI网站")
            print("3. 测试与AI的对话功能")
            print("4. 观察服务器日志中的导航过程")
        else:
            print("\n⚠️ 演示未能完全完成，请检查服务状态")
            
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
