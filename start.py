"""
启动脚本 - 简化的服务启动入口
提供更友好的启动方式和参数配置
"""

import sys
import argparse
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import main
from config import settings
from loguru import logger


def parse_arguments():
    """
    解析命令行参数
    
    使用思路：
    1. 提供灵活的启动配置选项
    2. 支持覆盖默认配置
    3. 便于开发和部署
    
    使用例子：
    python start.py --port 8080 --browser chrome --headless
    """
    parser = argparse.ArgumentParser(
        description="Browser AI服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start.py                          # 使用默认配置启动
  python start.py --port 8080              # 指定端口
  python start.py --browser firefox        # 指定浏览器
  python start.py --headless               # 无头模式
  python start.py --debug                  # 调试模式
  python start.py --host 0.0.0.0 --port 8000 --browser chrome --headless
        """
    )
    
    # 服务配置参数
    parser.add_argument(
        "--host",
        default=settings.api_host,
        help=f"API服务监听地址 (默认: {settings.api_host})"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=settings.api_port,
        help=f"API服务端口 (默认: {settings.api_port})"
    )
    
    # 浏览器配置参数
    parser.add_argument(
        "--browser",
        choices=["chrome", "firefox", "edge"],
        default=settings.browser_type,
        help=f"浏览器类型 (默认: {settings.browser_type})"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        default=settings.browser_headless,
        help="启用无头模式 (默认: 根据配置文件)"
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=settings.browser_timeout,
        help=f"浏览器操作超时时间(秒) (默认: {settings.browser_timeout})"
    )
    
    # 其他配置参数
    parser.add_argument(
        "--debug",
        action="store_true",
        default=settings.debug,
        help="启用调试模式 (默认: 根据配置文件)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=settings.log_level,
        help=f"日志级别 (默认: {settings.log_level})"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="检查依赖包是否已安装"
    )
    
    return parser.parse_args()


def check_dependencies():
    """
    检查必要的依赖包是否已安装
    
    使用思路：
    1. 在启动前验证环境
    2. 提供清晰的错误信息
    3. 指导用户安装缺失的依赖
    
    返回: 是否所有依赖都已安装
    """
    required_packages = [
        ("selenium", "selenium"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("loguru", "loguru"),
        ("webdriver_manager", "webdriver-manager"),
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            print(f"✓ {package_name}")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"✗ {package_name} (缺失)")
    
    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print(f"请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        print("或者:")
        print("pip install -r requirements.txt")
        return False
    
    print("\n所有依赖包都已安装 ✓")
    return True


def update_settings_from_args(args):
    """
    根据命令行参数更新配置
    
    参数: args - 解析后的命令行参数
    
    使用思路：
    1. 允许命令行参数覆盖配置文件
    2. 提供灵活的配置方式
    3. 便于测试和调试
    """
    # 更新API配置
    settings.api_host = args.host
    settings.api_port = args.port
    
    # 更新浏览器配置
    settings.browser_type = args.browser
    settings.browser_headless = args.headless
    settings.browser_timeout = args.timeout
    
    # 更新其他配置
    settings.debug = args.debug
    settings.log_level = args.log_level
    
    logger.info("配置已根据命令行参数更新")


def print_startup_info():
    """
    打印启动信息
    
    使用思路：
    1. 显示当前配置信息
    2. 提供有用的访问链接
    3. 帮助用户了解服务状态
    """
    print("=" * 60)
    print(f"🚀 Browser AI 服务启动")
    print("=" * 60)
    print(f"📡 API地址:     http://{settings.api_host}:{settings.api_port}")
    print(f"📚 API文档:     http://{settings.api_host}:{settings.api_port}/docs")
    print(f"🔍 ReDoc文档:   http://{settings.api_host}:{settings.api_port}/redoc")
    print(f"❤️  健康检查:   http://{settings.api_host}:{settings.api_port}/health")
    print("-" * 60)
    print(f"🌐 浏览器类型:  {settings.browser_type}")
    print(f"👻 无头模式:    {settings.browser_headless}")
    print(f"⏱️  超时时间:    {settings.browser_timeout}秒")
    print(f"🐛 调试模式:    {settings.debug}")
    print(f"📝 日志级别:    {settings.log_level}")
    print("=" * 60)
    print("💡 提示:")
    print("  - 使用 Ctrl+C 停止服务")
    print("  - 访问 /docs 查看完整API文档")
    print("  - 访问 /health 检查服务状态")
    print("=" * 60)


def main_with_args():
    """
    带参数解析的主函数
    
    使用思路：
    1. 解析命令行参数
    2. 更新配置
    3. 启动服务
    """
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 如果只是检查依赖，则执行检查后退出
        if args.check_deps:
            if check_dependencies():
                print("依赖检查通过！")
                sys.exit(0)
            else:
                sys.exit(1)
        
        # 检查依赖
        if not check_dependencies():
            print("\n❌ 依赖检查失败，请先安装缺失的依赖包")
            sys.exit(1)
        
        # 根据参数更新配置
        update_settings_from_args(args)
        
        # 打印启动信息
        print_startup_info()
        
        # 启动服务
        main()
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main_with_args()
