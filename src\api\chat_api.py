"""
聊天API - 提供OpenAI兼容的聊天完成接口
处理客户端的聊天请求，通过浏览器控制获取AI回复
"""

import uuid
import time
import asyncio
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from loguru import logger

from ..models.openai_models import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatMessage,
    ChatCompletionChoice,
    Usage,
    ErrorResponse
)
from ..browser_controller import <PERSON>rowserManager, InputHandler, ResponseMonitor, AIWebsiteManager
from config import settings

router = APIRouter()

# 全局浏览器管理器实例
browser_manager: Optional[BrowserManager] = None
input_handler: Optional[InputHandler] = None
response_monitor: Optional[ResponseMonitor] = None
ai_website_manager: Optional[AIWebsiteManager] = None


def get_browser_components():
    """
    获取浏览器组件实例
    返回: (browser_manager, input_handler, response_monitor, ai_website_manager)

    使用思路：
    1. 确保浏览器组件已初始化
    2. 提供依赖注入支持
    3. 统一管理浏览器实例
    4. 包含AI网站管理器
    """
    global browser_manager, input_handler, response_monitor, ai_website_manager

    if not browser_manager:
        browser_manager = BrowserManager()
        if not browser_manager.start_browser():
            raise HTTPException(status_code=500, detail="浏览器启动失败")

        input_handler = InputHandler(browser_manager)
        response_monitor = ResponseMonitor(browser_manager)
        ai_website_manager = AIWebsiteManager(browser_manager)

        logger.info("浏览器组件初始化完成")

        # 如果配置了自动导航，则导航到AI网站
        if settings.ai_website.auto_navigate_on_startup:
            logger.info("正在自动导航到AI网站...")
            if ai_website_manager.navigate_to_ai_website():
                # 更新输入和响应选择器
                ai_website_manager.update_input_handler_selectors(input_handler)
                ai_website_manager.update_response_monitor_selectors(response_monitor)
                logger.info("✅ AI网站导航和配置完成")
            else:
                logger.warning("⚠️ AI网站导航失败，将使用默认配置")

    return browser_manager, input_handler, response_monitor, ai_website_manager


def init_browser_on_startup():
    """
    在服务启动时初始化浏览器

    使用思路：
    1. 服务启动时就准备好浏览器
    2. 避免首次API调用的延迟
    3. 提前发现浏览器配置问题
    """
    try:
        get_browser_components()
        logger.info("启动时浏览器初始化成功")
        return True
    except Exception as e:
        logger.error(f"启动时浏览器初始化失败: {e}")
        return False


@router.post("/v1/chat/completions", response_model=ChatCompletionResponse)
async def create_chat_completion(request: ChatCompletionRequest):
    """
    创建聊天完成 - OpenAI兼容接口
    
    参数: request - 聊天完成请求
    返回: 聊天完成响应
    
    使用思路：
    1. 接收客户端的聊天请求
    2. 提取用户消息并通过浏览器输入
    3. 监听浏览器中的AI响应
    4. 返回OpenAI格式的响应
    
    使用例子：
    POST /v1/chat/completions
    {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "什么是人工智能？"}
        ]
    }
    """
    try:
        # 获取浏览器组件
        browser_mgr, input_hdl, response_mon, ai_mgr = get_browser_components()
        
        # 检查浏览器状态
        if not browser_mgr.is_browser_running():
            raise HTTPException(status_code=500, detail="浏览器未运行")
        
        # 提取用户消息（取最后一条用户消息）
        user_message = None
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_message = msg.content
                break
        
        if not user_message:
            raise HTTPException(status_code=400, detail="未找到用户消息")
        
        logger.info(f"处理用户消息: {user_message[:100]}...")
        
        # 等待输入框准备就绪
        if not input_hdl.wait_for_input_ready(timeout=10):
            raise HTTPException(status_code=500, detail="输入框未准备就绪")
        
        # 在浏览器中输入用户消息并提交
        if not input_hdl.input_and_submit(user_message):
            raise HTTPException(status_code=500, detail="输入消息失败")
        
        # 等待AI响应
        ai_response = response_mon.wait_for_response(timeout=60)
        
        if not ai_response:
            raise HTTPException(status_code=500, detail="未收到AI响应或响应超时")
        
        # 构建响应
        response_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
        
        choice = ChatCompletionChoice(
            index=0,
            message=ChatMessage(role="assistant", content=ai_response),
            finish_reason="stop"
        )
        
        # 简单的token计算（实际应用中可能需要更精确的计算）
        prompt_tokens = len(user_message.split()) * 1.3  # 粗略估算
        completion_tokens = len(ai_response.split()) * 1.3
        
        usage = Usage(
            prompt_tokens=int(prompt_tokens),
            completion_tokens=int(completion_tokens),
            total_tokens=int(prompt_tokens + completion_tokens)
        )
        
        response = ChatCompletionResponse(
            id=response_id,
            model=request.model,
            choices=[choice],
            usage=usage
        )
        
        logger.info(f"成功返回AI响应: {ai_response[:100]}...")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理聊天完成请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")


@router.post("/v1/chat/completions/stream")
async def create_chat_completion_stream(request: ChatCompletionRequest):
    """
    创建流式聊天完成 - OpenAI兼容接口
    
    参数: request - 聊天完成请求
    返回: 流式响应
    
    使用思路：
    1. 支持流式返回AI响应
    2. 实时推送响应内容
    3. 兼容OpenAI流式API格式
    
    注意：当前实现为简化版本，实际流式需要更复杂的实现
    """
    try:
        # 获取浏览器组件
        browser_mgr, input_hdl, response_mon, ai_mgr = get_browser_components()
        
        # 检查浏览器状态
        if not browser_mgr.is_browser_running():
            raise HTTPException(status_code=500, detail="浏览器未运行")
        
        # 提取用户消息
        user_message = None
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_message = msg.content
                break
        
        if not user_message:
            raise HTTPException(status_code=400, detail="未找到用户消息")
        
        async def generate_stream():
            """生成流式响应"""
            try:
                # 输入消息
                if not input_hdl.input_and_submit(user_message):
                    yield f"data: {{'error': '输入消息失败'}}\n\n"
                    return
                
                # 等待响应（简化实现，实际应该实时监听）
                ai_response = response_mon.wait_for_response(timeout=60)
                
                if ai_response:
                    # 模拟流式返回（将完整响应分块发送）
                    words = ai_response.split()
                    response_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
                    
                    for i, word in enumerate(words):
                        chunk_data = {
                            "id": response_id,
                            "object": "chat.completion.chunk",
                            "created": int(time.time()),
                            "model": request.model,
                            "choices": [{
                                "index": 0,
                                "delta": {"content": word + " "},
                                "finish_reason": None
                            }]
                        }
                        
                        yield f"data: {chunk_data}\n\n"
                        await asyncio.sleep(0.1)  # 模拟流式延迟
                    
                    # 发送结束标记
                    final_chunk = {
                        "id": response_id,
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {},
                            "finish_reason": "stop"
                        }]
                    }
                    
                    yield f"data: {final_chunk}\n\n"
                    yield "data: [DONE]\n\n"
                else:
                    yield f"data: {{'error': '未收到AI响应'}}\n\n"
                    
            except Exception as e:
                logger.error(f"流式响应生成失败: {e}")
                yield f"data: {{'error': '{str(e)}'}}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )
        
    except Exception as e:
        logger.error(f"创建流式聊天完成失败: {e}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")
