"""
测试智能选择器功能
验证指定选择器优先，找不到时自动回退的功能
"""

import sys
import os
import time
from loguru import logger

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.browser_controller.browser_manager import BrowserManager
from src.browser_controller.input_handler import InputHandler


def test_website_config():
    """测试网站配置功能"""
    print("🌐 测试网站配置功能")
    print("-" * 50)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = Input<PERSON>andler(browser_manager)
    
    # 测试不同网站的配置
    test_cases = [
        ("https://chat.openai.com", "ChatGPT"),
        ("https://claude.ai", "Claude"),
        ("https://space.coze.cn/task/123", "Coze扣子"),
        ("https://tongyi.aliyun.com", "通义千问"),
        ("https://example.com", "通用网站"),
    ]
    
    for url, expected_name in test_cases:
        try:
            print(f"\n📍 测试 {expected_name}: {url}")
            
            # 模拟导航到测试URL
            browser_manager.driver.get("data:text/html,<html><head><title>Test</title></head><body><h1>Test Page</h1></body></html>")
            browser_manager.driver.execute_script(f"window.history.replaceState(null, null, '{url}');")
            
            # 获取网站配置
            config = input_handler.get_website_config()
            
            print(f"   网站名称: {config['name']}")
            print(f"   选择器数量: {len(config['selectors'])}")
            print(f"   占位符文本: {config['placeholder_texts']}")
            print(f"   提交方式: {config['submit_methods']}")
            print(f"   等待时间: {config['wait_time']}秒")
            
            # 显示前3个选择器的详细信息
            print(f"   前3个选择器:")
            for i, selector_info in enumerate(config['selectors'][:3]):
                print(f"     {i+1}. {selector_info['selector']} ({selector_info['type']}) - {selector_info['description']}")
            
            if len(config['selectors']) > 3:
                print(f"     ... 还有 {len(config['selectors']) - 3} 个选择器")
            
            # 验证网站名称
            if config['name'] == expected_name:
                print(f"   ✅ 网站识别正确")
            else:
                print(f"   ⚠️ 网站识别: 期望 {expected_name}, 实际 {config['name']}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 关闭浏览器
    browser_manager.driver.quit()
    print("\n✅ 网站配置测试完成")
    return True


def test_smart_search():
    """测试智能搜索功能"""
    print("\n🧠 测试智能搜索功能")
    print("-" * 50)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = InputHandler(browser_manager)
    
    # 创建测试页面
    test_html = """
    <html>
    <head><title>Smart Search Test</title></head>
    <body>
        <h1>智能搜索测试页面</h1>
        
        <!-- 高优先级输入框 -->
        <div>
            <label>高优先级输入框:</label>
            <input type="text" id="high-priority" placeholder="高优先级输入框" data-priority="high">
        </div>
        
        <!-- 中优先级输入框 -->
        <div>
            <label>中优先级输入框:</label>
            <textarea id="medium-priority" placeholder="中优先级输入框" data-priority="medium"></textarea>
        </div>
        
        <!-- 低优先级输入框 -->
        <div>
            <label>低优先级输入框:</label>
            <input type="text" class="low-priority" placeholder="低优先级输入框" data-priority="low">
        </div>
        
        <!-- 隐藏的输入框 -->
        <div>
            <input type="text" id="hidden-input" placeholder="隐藏输入框" style="display:none;">
        </div>
    </body>
    </html>
    """
    
    try:
        # 加载测试页面
        browser_manager.driver.get("data:text/html," + test_html)
        time.sleep(1)
        
        # 模拟为ChatGPT网站
        browser_manager.driver.execute_script("window.history.replaceState(null, null, 'https://chat.openai.com');")
        
        print("📋 测试场景:")
        
        # 测试场景1: 指定存在的选择器
        print("\n1. 指定存在的选择器 (#high-priority)")
        element = input_handler.find_input_element_smart("#high-priority")
        if element:
            element_id = element.get_attribute('id')
            print(f"   ✅ 找到元素: {element_id}")
        else:
            print(f"   ❌ 未找到元素")
        
        # 测试场景2: 指定不存在的选择器，应该自动回退
        print("\n2. 指定不存在的选择器 (#non-existent)，应该自动回退")
        element = input_handler.find_input_element_smart("#non-existent")
        if element:
            element_id = element.get_attribute('id') or element.get_attribute('class') or element.tag_name
            print(f"   ✅ 自动回退成功，找到元素: {element_id}")
        else:
            print(f"   ❌ 自动回退失败，未找到元素")
        
        # 测试场景3: 不指定选择器，使用网站特定配置
        print("\n3. 不指定选择器，使用ChatGPT网站特定配置")
        element = input_handler.find_input_element_smart()
        if element:
            element_info = element.get_attribute('id') or element.get_attribute('class') or element.tag_name
            print(f"   ✅ 使用网站配置成功，找到元素: {element_info}")
        else:
            print(f"   ❌ 使用网站配置失败，未找到元素")
        
        # 测试场景4: XPath选择器
        print("\n4. 使用XPath选择器")
        element = input_handler.find_input_element_smart("//textarea[@id='medium-priority']")
        if element:
            element_id = element.get_attribute('id')
            print(f"   ✅ XPath选择器成功，找到元素: {element_id}")
        else:
            print(f"   ❌ XPath选择器失败，未找到元素")
        
        # 测试场景5: 输入文本测试
        print("\n5. 智能输入文本测试")
        success = input_handler.input_text("智能搜索测试文本", "#high-priority", use_smart_search=True)
        if success:
            print(f"   ✅ 智能输入成功")
            # 验证输入结果
            element = browser_manager.driver.find_element("id", "high-priority")
            value = element.get_attribute("value")
            print(f"   📝 输入内容: {value}")
        else:
            print(f"   ❌ 智能输入失败")
        
        # 测试场景6: 等待输入准备就绪
        print("\n6. 智能等待输入准备就绪测试")
        ready = input_handler.wait_for_input_ready("#medium-priority", timeout=3, use_smart_search=True)
        if ready:
            print(f"   ✅ 智能等待成功")
        else:
            print(f"   ❌ 智能等待失败")
        
        print("\n✅ 智能搜索功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 关闭浏览器
        browser_manager.driver.quit()


def main():
    """主函数"""
    print("🚀 智能选择器功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试网站配置功能
        if test_website_config():
            print("✅ 网站配置功能测试通过")
        else:
            print("❌ 网站配置功能测试失败")
        
        # 2. 测试智能搜索功能
        if test_smart_search():
            print("✅ 智能搜索功能测试通过")
        else:
            print("❌ 智能搜索功能测试失败")
        
        print("\n" + "=" * 60)
        print("🎉 智能选择器功能测试完成！")
        
        print("\n💡 功能总结:")
        print("✅ 网站特定配置 - 每个网站都有专门的选择器配置")
        print("✅ 选择器优先级 - 按优先级排序，提高匹配准确性")
        print("✅ 智能回退机制 - 指定选择器失败时自动回退")
        print("✅ 多种选择器类型 - 支持CSS、XPath、ID、Class等")
        print("✅ 详细的搜索日志 - 便于调试和优化")
        
        print("\n🔧 支持的功能:")
        print("• 指定选择器优先，找不到时自动回退")
        print("• 网站特定配置，针对不同AI网站优化")
        print("• 选择器类型自动识别和处理")
        print("• 详细的搜索过程记录")
        print("• 元素有效性验证")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
