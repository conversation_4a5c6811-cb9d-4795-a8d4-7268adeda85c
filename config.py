"""
配置文件 - 管理项目的各种配置参数
包含浏览器设置、API设置、日志设置等
"""

import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用程序配置类"""
    
    # API服务配置
    api_host: str = "0.0.0.0"  # API服务监听地址
    api_port: int = 8000  # API服务端口
    api_title: str = "Browser AI API"  # API标题
    api_version: str = "1.0.0"  # API版本
    
    # 浏览器配置
    browser_type: str = "chrome"  # 浏览器类型 (chrome, firefox, edge)
    browser_headless: bool = False  # 是否无头模式运行
    browser_timeout: int = 30  # 浏览器操作超时时间(秒)
    browser_window_size: str = "1920,1080"  # 浏览器窗口大小
    
    # AI模型配置
    model_name: str = "gpt-3.5-turbo"  # 默认模型名称
    max_tokens: int = 2048  # 最大token数
    temperature: float = 0.7  # 温度参数
    
    # 日志配置
    log_level: str = "INFO"  # 日志级别
    log_file: str = "browser_ai.log"  # 日志文件路径
    
    # 其他配置
    debug: bool = False  # 调试模式
    cors_origins: list = ["*"]  # CORS允许的源
    
    class Config:
        env_file = ".env"  # 环境变量文件
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
