"""
配置文件 - 管理项目的各种配置参数
包含浏览器设置、API设置、日志设置等
"""

import os
from typing import Optional, List, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field


class BrowserConfig(BaseSettings):
    """
    浏览器配置类
    包含浏览器相关的所有配置参数
    """

    # 基本浏览器配置
    browser_type: str = Field(default="chrome", description="浏览器类型 (chrome, firefox, edge)")
    browser_headless: bool = Field(default=False, description="是否无头模式运行")
    browser_timeout: int = Field(default=30, description="浏览器操作超时时间(秒)")
    browser_window_size: str = Field(default="1920,1080", description="浏览器窗口大小 (宽,高)")

    # 浏览器路径配置 - 当自动检测失败时使用
    chrome_binary_path: Optional[str] = Field(default=None, description="Chrome浏览器可执行文件路径")
    firefox_binary_path: Optional[str] = Field(default=None, description="Firefox浏览器可执行文件路径")
    edge_binary_path: Optional[str] = Field(default=None, description="Edge浏览器可执行文件路径")

    # 驱动程序配置
    chrome_driver_path: Optional[str] = Field(default=None, description="ChromeDriver路径")
    firefox_driver_path: Optional[str] = Field(default=None, description="GeckoDriver路径")
    edge_driver_path: Optional[str] = Field(default=None, description="EdgeDriver路径")

    # 浏览器启动参数
    chrome_options: List[str] = Field(default_factory=lambda: [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--disable-blink-features=AutomationControlled",
        "--disable-extensions"
    ], description="Chrome启动参数")

    firefox_options: List[str] = Field(default_factory=lambda: [
        "--disable-gpu",
        "--no-sandbox"
    ], description="Firefox启动参数")

    edge_options: List[str] = Field(default_factory=lambda: [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu"
    ], description="Edge启动参数")

    # 浏览器偏好设置
    chrome_prefs: Dict[str, Any] = Field(default_factory=lambda: {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.managed_default_content_settings.images": 2  # 禁用图片加载以提高速度
    }, description="Chrome偏好设置")

    # 页面加载配置
    page_load_timeout: int = Field(default=30, description="页面加载超时时间(秒)")
    implicit_wait: int = Field(default=10, description="隐式等待时间(秒)")
    script_timeout: int = Field(default=30, description="脚本执行超时时间(秒)")

    # 重试配置
    browser_start_retry_count: int = Field(default=3, description="浏览器启动重试次数")
    browser_start_retry_delay: int = Field(default=2, description="浏览器启动重试延迟(秒)")


class InputConfig(BaseSettings):
    """
    输入处理配置类
    包含输入框识别和文本输入相关配置
    """

    # 输入框选择器配置 - 按优先级排序
    input_selectors: List[str] = Field(default_factory=lambda: [
        'input[type="text"]',
        'input[type="search"]',
        'textarea',
        'input:not([type="hidden"]):not([type="submit"]):not([type="button"])',
        '[contenteditable="true"]',
        '.input',
        '.search-input',
        '.chat-input',
        '.message-input',
        '#input',
        '#search',
        '#chat-input',
        '[placeholder*="输入"]',
        '[placeholder*="搜索"]',
        '[placeholder*="问"]'
    ], description="输入框选择器列表，按优先级排序")

    # 输入行为配置
    input_delay: float = Field(default=0.1, description="输入字符间延迟(秒)")
    clear_before_input: bool = Field(default=True, description="输入前是否清空输入框")
    input_retry_count: int = Field(default=3, description="输入失败重试次数")
    input_retry_delay: float = Field(default=1.0, description="输入重试延迟(秒)")

    # 提交配置
    submit_methods: List[str] = Field(default_factory=lambda: [
        "enter_key",  # 按回车键
        "submit_button",  # 点击提交按钮
        "form_submit"  # 表单提交
    ], description="提交方法优先级")

    submit_button_selectors: List[str] = Field(default_factory=lambda: [
        'button[type="submit"]',
        'input[type="submit"]',
        '.submit-btn',
        '.send-btn',
        '.search-btn',
        'button:contains("发送")',
        'button:contains("提交")',
        'button:contains("搜索")'
    ], description="提交按钮选择器")


class ResponseConfig(BaseSettings):
    """
    响应监听配置类
    包含AI响应识别和监听相关配置
    """

    # 响应内容选择器配置
    response_selectors: List[str] = Field(default_factory=lambda: [
        '.response',
        '.answer',
        '.reply',
        '.message',
        '.chat-message',
        '.ai-response',
        '.bot-message',
        '.assistant-message',
        '[data-role="response"]',
        '[data-type="answer"]',
        '[data-message-author-role="assistant"]',
        '.markdown',
        '.message-content'
    ], description="响应内容选择器列表")

    # 响应等待配置
    response_timeout: int = Field(default=60, description="等待响应超时时间(秒)")
    response_check_interval: float = Field(default=0.5, description="响应检查间隔(秒)")
    response_stable_time: float = Field(default=2.0, description="响应稳定时间(秒) - 内容不变化的时间")

    # 响应过滤配置
    min_response_length: int = Field(default=5, description="最小响应长度")
    max_response_length: int = Field(default=50000, description="最大响应长度")
    ignore_empty_responses: bool = Field(default=True, description="是否忽略空响应")

    # 响应处理配置
    clean_response_text: bool = Field(default=True, description="是否清理响应文本")
    remove_html_tags: bool = Field(default=True, description="是否移除HTML标签")
    normalize_whitespace: bool = Field(default=True, description="是否规范化空白字符")


class AIWebsiteConfig(BaseSettings):
    """
    AI网站配置类
    包含不同AI网站的URL和选择器配置
    """

    # 默认AI网站配置
    default_ai_website: str = Field(default="chatgpt", description="默认AI网站 (chatgpt/claude/bard/custom)")
    auto_navigate_on_startup: bool = Field(default=True, description="启动时是否自动导航到AI网站")

    # AI网站URL配置
    chatgpt_url: str = Field(default="https://chat.openai.com", description="ChatGPT网站URL")
    claude_url: str = Field(default="https://claude.ai", description="Claude网站URL")
    bard_url: str = Field(default="https://bard.google.com", description="Google Bard网站URL")
    custom_ai_url: str = Field(default="", description="自定义AI网站URL")

    # 等待页面加载的时间
    page_load_wait_time: int = Field(default=5, description="页面加载后等待时间(秒)")

    # ChatGPT特定配置
    chatgpt_input_selectors: List[str] = Field(default_factory=lambda: [
        'textarea[data-id="root"]',
        'textarea[placeholder*="Message"]',
        'textarea[placeholder*="Send a message"]',
        '#prompt-textarea',
        'textarea'
    ], description="ChatGPT输入框选择器")

    chatgpt_response_selectors: List[str] = Field(default_factory=lambda: [
        '[data-message-author-role="assistant"]',
        '.markdown',
        '.message-content',
        '[data-testid="conversation-turn-content"]'
    ], description="ChatGPT响应选择器")

    # Claude特定配置
    claude_input_selectors: List[str] = Field(default_factory=lambda: [
        'div[contenteditable="true"]',
        '.ProseMirror',
        '[data-testid="chat-input"]',
        'textarea'
    ], description="Claude输入框选择器")

    claude_response_selectors: List[str] = Field(default_factory=lambda: [
        '[data-testid="conversation-turn-content"]',
        '.font-claude-message',
        '.prose',
        '.message-content'
    ], description="Claude响应选择器")

    # Bard特定配置
    bard_input_selectors: List[str] = Field(default_factory=lambda: [
        'rich-textarea[placeholder*="Enter a prompt"]',
        '.ql-editor',
        '[data-test-id="input-area"]',
        'textarea'
    ], description="Bard输入框选择器")

    bard_response_selectors: List[str] = Field(default_factory=lambda: [
        '[data-test-id="bot-response"]',
        '.model-response-text',
        '.response-container',
        '.message-content'
    ], description="Bard响应选择器")


class Settings(BaseSettings):
    """应用程序主配置类"""

    # API服务配置
    api_host: str = Field(default="localhost", description="API服务监听地址")
    api_port: int = Field(default=8000, description="API服务端口")
    api_title: str = Field(default="Browser AI API", description="API标题")
    api_version: str = Field(default="1.0.0", description="API版本")

    # AI模型配置
    model_name: str = Field(default="gpt-3.5-turbo", description="默认模型名称")
    max_tokens: int = Field(default=2048, description="最大token数")
    temperature: float = Field(default=0.7, description="温度参数")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="browser_ai.log", description="日志文件路径")
    log_rotation: str = Field(default="10 MB", description="日志文件轮转大小")
    log_retention: str = Field(default="7 days", description="日志保留时间")

    # 其他配置
    debug: bool = Field(default=False, description="调试模式")
    cors_origins: List[str] = Field(default_factory=lambda: ["*"], description="CORS允许的源")

    # 性能配置
    max_concurrent_requests: int = Field(default=10, description="最大并发请求数")
    request_timeout: int = Field(default=300, description="请求超时时间(秒)")

    # 子配置
    browser: BrowserConfig = Field(default_factory=BrowserConfig, description="浏览器配置")
    input: InputConfig = Field(default_factory=InputConfig, description="输入配置")
    response: ResponseConfig = Field(default_factory=ResponseConfig, description="响应配置")
    ai_website: AIWebsiteConfig = Field(default_factory=AIWebsiteConfig, description="AI网站配置")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        # 支持嵌套配置
        env_nested_delimiter = "__"
        # 解决模型名称冲突警告
        protected_namespaces = ('settings_',)


# 全局配置实例
settings = Settings()
