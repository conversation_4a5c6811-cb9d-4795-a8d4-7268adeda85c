"""
快速启动测试脚本 - 诊断浏览器启动问题
"""

import os
import sys
import time
import subprocess
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager


def test_chrome_path():
    """测试Chrome路径"""
    print("🔍 检查Chrome安装路径...")
    
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome: {path}")
            return path
        else:
            print(f"❌ 未找到: {path}")
    
    return None


def test_chrome_version():
    """测试Chrome版本"""
    print("\n🔍 检查Chrome版本...")
    try:
        result = subprocess.run([
            r"C:\Program Files\Google\Chrome\Application\chrome.exe", 
            "--version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Chrome版本: {version}")
            return version
        else:
            print(f"❌ 获取版本失败: {result.stderr}")
            return None
    except Exception as e:
        print(f"❌ 版本检查异常: {e}")
        return None


def test_chromedriver():
    """测试ChromeDriver"""
    print("\n🔍 检查ChromeDriver...")
    try:
        print("正在下载/检查ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver路径: {driver_path}")
        
        # 测试ChromeDriver版本
        result = subprocess.run([driver_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ChromeDriver版本: {version}")
            return driver_path
        else:
            print(f"❌ ChromeDriver版本检查失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ ChromeDriver检查异常: {e}")
        return None


def test_minimal_chrome():
    """测试最小化Chrome启动"""
    print("\n🔍 测试最小化Chrome启动...")
    
    chrome_path = test_chrome_path()
    driver_path = test_chromedriver()
    
    if not chrome_path or not driver_path:
        print("❌ Chrome或ChromeDriver不可用")
        return False
    
    try:
        print("正在配置Chrome选项...")
        options = webdriver.ChromeOptions()
        
        # 最小化配置
        minimal_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--headless=new',
            '--remote-debugging-port=0',
            '--user-data-dir=C:\\temp\\chrome_test'
        ]
        
        for arg in minimal_args:
            options.add_argument(arg)
        
        options.binary_location = chrome_path
        
        print("正在创建ChromeDriver服务...")
        service = ChromeService(driver_path)
        
        print("正在启动Chrome浏览器...")
        print("⏳ 这可能需要一些时间，请耐心等待...")
        
        start_time = time.time()
        driver = webdriver.Chrome(service=service, options=options)
        end_time = time.time()
        
        print(f"✅ Chrome启动成功！耗时: {end_time - start_time:.2f}秒")
        
        # 测试基本功能
        print("正在测试基本功能...")
        driver.get("data:text/html,<html><body><h1>Test Page</h1></body></html>")
        title = driver.title
        print(f"✅ 页面标题: {title}")
        
        # 关闭浏览器
        driver.quit()
        print("✅ 浏览器已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        return False


def test_process_cleanup():
    """清理Chrome进程"""
    print("\n🧹 清理Chrome进程...")
    try:
        # 杀死所有Chrome进程
        subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"], 
                      capture_output=True, timeout=10)
        subprocess.run(["taskkill", "/F", "/IM", "chromedriver.exe"], 
                      capture_output=True, timeout=10)
        print("✅ Chrome进程清理完成")
        
        # 清理临时目录
        import shutil
        temp_dir = r"C:\temp\chrome_test"
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print("✅ 临时目录清理完成")
            except:
                print("⚠️ 临时目录清理失败（可能被占用）")
                
    except Exception as e:
        print(f"⚠️ 进程清理异常: {e}")


def main():
    """主函数"""
    print("🚀 Browser AI Chrome启动诊断工具")
    print("=" * 50)
    
    # 清理之前的进程
    test_process_cleanup()
    
    # 检查Chrome
    chrome_version = test_chrome_version()
    
    # 检查ChromeDriver
    driver_path = test_chromedriver()
    
    # 测试启动
    if test_minimal_chrome():
        print("\n🎉 诊断结果: Chrome启动正常！")
        print("\n💡 建议:")
        print("1. 重新启动Browser AI服务")
        print("2. 如果仍有问题，请检查防火墙设置")
        print("3. 确保没有其他程序占用Chrome")
    else:
        print("\n❌ 诊断结果: Chrome启动失败！")
        print("\n🔧 可能的解决方案:")
        print("1. 重启计算机")
        print("2. 更新Chrome浏览器")
        print("3. 检查系统防火墙设置")
        print("4. 确保有足够的系统资源")
        print("5. 尝试以管理员身份运行")
    
    # 最终清理
    test_process_cleanup()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 诊断被用户中断")
        test_process_cleanup()
    except Exception as e:
        print(f"\n💥 诊断过程中发生错误: {e}")
        test_process_cleanup()
