"""
API接口测试
测试聊天完成和浏览器控制接口的功能
"""

import pytest
import requests
import time
from typing import Dict, Any


class TestBrowserAI:
    """Browser AI API测试类"""
    
    def __init__(self):
        """
        初始化测试类
        
        使用思路：
        1. 设置测试的基础URL
        2. 配置测试参数
        3. 准备测试数据
        """
        self.base_url = "http://localhost:8000"
        self.timeout = 30
        
    def test_health_check(self):
        """
        测试健康检查接口
        
        使用思路：
        1. 验证服务是否正常运行
        2. 检查返回的状态信息
        
        使用例子：
        pytest tests/test_api.py::TestBrowserAI::test_health_check -v
        """
        try:
            response = requests.get(f"{self.base_url}/health", timeout=self.timeout)
            
            assert response.status_code == 200, f"健康检查失败，状态码: {response.status_code}"
            
            data = response.json()
            assert data["status"] == "healthy", f"服务状态不健康: {data.get('status')}"
            
            print("✓ 健康检查通过")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 健康检查失败: {e}")
            return False
    
    def test_browser_status(self):
        """
        测试浏览器状态接口
        
        使用思路：
        1. 检查浏览器是否正常启动
        2. 验证状态信息的完整性
        
        使用例子：
        pytest tests/test_api.py::TestBrowserAI::test_browser_status -v
        """
        try:
            response = requests.get(f"{self.base_url}/browser/status", timeout=self.timeout)
            
            assert response.status_code == 200, f"获取浏览器状态失败，状态码: {response.status_code}"
            
            data = response.json()
            assert "running" in data, "响应中缺少running字段"
            assert "browser_type" in data, "响应中缺少browser_type字段"
            
            print(f"✓ 浏览器状态: 运行中={data['running']}, 类型={data['browser_type']}")
            return data
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 获取浏览器状态失败: {e}")
            return None
    
    def test_browser_navigate(self, url: str = "https://www.baidu.com"):
        """
        测试浏览器导航功能
        
        参数: url - 要导航到的URL
        
        使用思路：
        1. 测试浏览器导航到指定网站
        2. 验证导航是否成功
        
        使用例子：
        test = TestBrowserAI()
        test.test_browser_navigate("https://www.google.com")
        """
        try:
            payload = {
                "url": url,
                "wait_for_load": True
            }
            
            response = requests.post(
                f"{self.base_url}/browser/navigate",
                json=payload,
                timeout=self.timeout
            )
            
            assert response.status_code == 200, f"浏览器导航失败，状态码: {response.status_code}"
            
            data = response.json()
            assert data.get("success") == True, f"导航失败: {data}"
            
            print(f"✓ 浏览器导航成功: {url}")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 浏览器导航失败: {e}")
            return False
    
    def test_browser_input(self, text: str = "测试输入", submit: bool = False):
        """
        测试浏览器输入功能
        
        参数:
            text - 要输入的文本
            submit - 是否提交
        
        使用思路：
        1. 测试在浏览器中输入文本
        2. 验证输入操作是否成功
        
        使用例子：
        test = TestBrowserAI()
        test.test_browser_input("Hello World", submit=True)
        """
        try:
            payload = {
                "text": text,
                "submit": submit
            }
            
            response = requests.post(
                f"{self.base_url}/browser/input",
                json=payload,
                timeout=self.timeout
            )
            
            assert response.status_code == 200, f"浏览器输入失败，状态码: {response.status_code}"
            
            data = response.json()
            assert data.get("success") == True, f"输入失败: {data}"
            
            print(f"✓ 浏览器输入成功: {text} (提交: {submit})")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 浏览器输入失败: {e}")
            return False
    
    def test_chat_completion(self, message: str = "你好，请简单介绍一下自己"):
        """
        测试聊天完成接口
        
        参数: message - 要发送的消息
        
        使用思路：
        1. 测试OpenAI兼容的聊天接口
        2. 验证AI响应的完整性
        
        使用例子：
        test = TestBrowserAI()
        response = test.test_chat_completion("什么是人工智能？")
        """
        try:
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": message}
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            print(f"发送消息: {message}")
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=120  # 聊天可能需要更长时间
            )
            
            assert response.status_code == 200, f"聊天完成失败，状态码: {response.status_code}"
            
            data = response.json()
            assert "choices" in data, "响应中缺少choices字段"
            assert len(data["choices"]) > 0, "choices数组为空"
            assert "message" in data["choices"][0], "choice中缺少message字段"
            
            ai_response = data["choices"][0]["message"]["content"]
            print(f"✓ AI回复: {ai_response[:100]}...")
            
            return ai_response
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 聊天完成失败: {e}")
            return None
    
    def run_full_test(self):
        """
        运行完整的测试套件
        
        使用思路：
        1. 按顺序执行所有测试
        2. 记录测试结果
        3. 提供测试报告
        
        使用例子：
        test = TestBrowserAI()
        test.run_full_test()
        """
        print("=" * 60)
        print("🧪 开始Browser AI完整测试")
        print("=" * 60)
        
        test_results = {}
        
        # 1. 健康检查
        print("\n1. 测试健康检查...")
        test_results["health_check"] = self.test_health_check()
        
        # 2. 浏览器状态
        print("\n2. 测试浏览器状态...")
        browser_status = self.test_browser_status()
        test_results["browser_status"] = browser_status is not None
        
        # 3. 浏览器导航
        print("\n3. 测试浏览器导航...")
        test_results["browser_navigate"] = self.test_browser_navigate()
        
        # 4. 浏览器输入（如果导航成功）
        if test_results["browser_navigate"]:
            print("\n4. 测试浏览器输入...")
            test_results["browser_input"] = self.test_browser_input()
        else:
            print("\n4. 跳过浏览器输入测试（导航失败）")
            test_results["browser_input"] = False
        
        # 5. 聊天完成（需要先导航到AI网站）
        print("\n5. 测试聊天完成...")
        # 注意：这里可能需要先导航到具体的AI网站
        # test_results["chat_completion"] = self.test_chat_completion() is not None
        print("   注意：聊天完成测试需要先导航到AI网站")
        test_results["chat_completion"] = None
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📊 测试结果报告")
        print("=" * 60)
        
        passed = 0
        total = 0
        
        for test_name, result in test_results.items():
            if result is not None:
                total += 1
                if result:
                    passed += 1
                    status = "✓ 通过"
                else:
                    status = "✗ 失败"
            else:
                status = "⚠ 跳过"
            
            print(f"{test_name:20} : {status}")
        
        print("-" * 60)
        if total > 0:
            success_rate = (passed / total) * 100
            print(f"测试通过率: {passed}/{total} ({success_rate:.1f}%)")
        else:
            print("没有执行任何测试")
        
        print("=" * 60)
        
        return test_results


def main():
    """
    主测试函数
    
    使用思路：
    1. 创建测试实例
    2. 运行测试套件
    3. 处理测试结果
    
    使用例子：
    python tests/test_api.py
    """
    try:
        print("🚀 启动Browser AI API测试")
        
        # 创建测试实例
        test = TestBrowserAI()
        
        # 运行完整测试
        results = test.run_full_test()
        
        # 检查是否有失败的测试
        failed_tests = [name for name, result in results.items() if result is False]
        
        if failed_tests:
            print(f"\n❌ 有 {len(failed_tests)} 个测试失败: {', '.join(failed_tests)}")
            return 1
        else:
            print(f"\n✅ 所有测试都通过了！")
            return 0
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
