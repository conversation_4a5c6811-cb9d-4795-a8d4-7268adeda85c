"""
浏览器管理器 - 负责浏览器的启动、配置和基本操作
支持Chrome、Firefox、Edge等主流浏览器
"""

import time
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from loguru import logger

from config import settings


class BrowserManager:
    """浏览器管理器类"""
    
    def __init__(self):
        """初始化浏览器管理器"""
        self.driver: Optional[webdriver.Remote] = None
        self.wait: Optional[WebDriverWait] = None
        self.browser_type = settings.browser_type.lower()
        self.headless = settings.browser_headless
        self.timeout = settings.browser_timeout
        
    def start_browser(self) -> bool:
        """
        启动浏览器
        返回: 启动是否成功
        """
        try:
            if self.browser_type == "chrome":
                self._start_chrome()
            elif self.browser_type == "firefox":
                self._start_firefox()
            elif self.browser_type == "edge":
                self._start_edge()
            else:
                raise ValueError(f"不支持的浏览器类型: {self.browser_type}")
            
            # 设置等待对象
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            # 设置窗口大小
            if not self.headless:
                width, height = map(int, settings.browser_window_size.split(','))
                self.driver.set_window_size(width, height)
            
            logger.info(f"浏览器 {self.browser_type} 启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            return False
    
    def _start_chrome(self):
        """启动Chrome浏览器"""
        options = webdriver.ChromeOptions()
        
        # 基本配置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        
        # 无头模式配置
        if self.headless:
            options.add_argument('--headless')
        
        # 禁用通知和弹窗
        prefs = {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0
        }
        options.add_experimental_option("prefs", prefs)
        
        # 创建服务对象
        service = ChromeService(ChromeDriverManager().install())
        
        # 启动浏览器
        self.driver = webdriver.Chrome(service=service, options=options)
        
    def _start_firefox(self):
        """启动Firefox浏览器"""
        options = webdriver.FirefoxOptions()
        
        if self.headless:
            options.add_argument('--headless')
        
        service = FirefoxService(GeckoDriverManager().install())
        self.driver = webdriver.Firefox(service=service, options=options)
        
    def _start_edge(self):
        """启动Edge浏览器"""
        options = webdriver.EdgeOptions()
        
        if self.headless:
            options.add_argument('--headless')
        
        service = EdgeService(EdgeChromiumDriverManager().install())
        self.driver = webdriver.Edge(service=service, options=options)
    
    def navigate_to(self, url: str) -> bool:
        """
        导航到指定URL
        参数: url - 目标URL
        返回: 导航是否成功
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return False
                
            self.driver.get(url)
            logger.info(f"导航到: {url}")
            return True
            
        except Exception as e:
            logger.error(f"导航失败: {e}")
            return False
    
    def find_element(self, by: By, value: str, timeout: Optional[int] = None) -> Optional[Any]:
        """
        查找页面元素
        参数: 
            by - 查找方式 (By.ID, By.CLASS_NAME等)
            value - 查找值
            timeout - 超时时间(秒)，默认使用全局配置
        返回: 找到的元素或None
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
            
            wait_time = timeout or self.timeout
            wait = WebDriverWait(self.driver, wait_time)
            element = wait.until(EC.presence_of_element_located((by, value)))
            
            return element
            
        except Exception as e:
            logger.error(f"查找元素失败 {by}='{value}': {e}")
            return None
    
    def get_page_source(self) -> Optional[str]:
        """
        获取当前页面源码
        返回: 页面源码或None
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
                
            return self.driver.page_source
            
        except Exception as e:
            logger.error(f"获取页面源码失败: {e}")
            return None
    
    def execute_script(self, script: str) -> Any:
        """
        执行JavaScript脚本
        参数: script - JavaScript代码
        返回: 脚本执行结果
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
                
            return self.driver.execute_script(script)
            
        except Exception as e:
            logger.error(f"执行脚本失败: {e}")
            return None
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("浏览器已关闭")
                
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
    
    def is_browser_running(self) -> bool:
        """
        检查浏览器是否正在运行
        返回: 浏览器运行状态
        """
        try:
            if not self.driver:
                return False
            
            # 尝试获取当前URL来检查浏览器状态
            _ = self.driver.current_url
            return True
            
        except Exception:
            return False
