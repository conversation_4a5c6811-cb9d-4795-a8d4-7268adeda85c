"""
浏览器管理器 - 负责浏览器的启动、配置和基本操作
支持Chrome、Firefox、Edge等主流浏览器
"""

import time
import threading
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from loguru import logger

from config import settings


class BrowserManager:
    """浏览器管理器类"""
    
    def __init__(self):
        """初始化浏览器管理器"""
        self.driver: Optional[webdriver.Remote] = None
        self.wait: Optional[WebDriverWait] = None
        self.browser_type = settings.browser.browser_type.lower()
        self.headless = settings.browser.browser_headless
        self.timeout = settings.browser.browser_timeout
        self.config = settings.browser
        
    def start_browser(self) -> bool:
        """
        启动浏览器
        返回: 启动是否成功
        """
        # 重试机制
        for attempt in range(self.config.browser_start_retry_count):
            try:
                if self.browser_type == "chrome":
                    self._start_chrome()
                elif self.browser_type == "firefox":
                    self._start_firefox()
                elif self.browser_type == "edge":
                    self._start_edge()
                else:
                    raise ValueError(f"不支持的浏览器类型: {self.browser_type}")

                # 设置等待对象和超时时间
                self.wait = WebDriverWait(self.driver, self.timeout)
                self.driver.set_page_load_timeout(self.config.page_load_timeout)
                self.driver.implicitly_wait(self.config.implicit_wait)
                self.driver.set_script_timeout(self.config.script_timeout)

                # 设置窗口大小
                if not self.headless:
                    width, height = map(int, self.config.browser_window_size.split(','))
                    self.driver.set_window_size(width, height)

                logger.info(f"浏览器 {self.browser_type} 启动成功")
                return True

            except Exception as e:
                logger.error(f"第 {attempt + 1} 次启动浏览器失败: {e}")

                if attempt < self.config.browser_start_retry_count - 1:
                    logger.info(f"{self.config.browser_start_retry_delay}秒后重试...")
                    time.sleep(self.config.browser_start_retry_delay)
                else:
                    logger.error(f"经过 {self.config.browser_start_retry_count} 次尝试后仍然失败")
                    return False

        return False
    
    def _start_chrome(self):
        """启动Chrome浏览器"""
        logger.info("开始配置Chrome选项...")
        options = webdriver.ChromeOptions()

        # 添加基础稳定性参数
        stability_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-javascript',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-back-forward-cache',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-client-side-phishing-detection',
            '--disable-component-update',
            '--disable-domain-reliability',
            '--disable-background-networking',
            '--disable-breakpad',
            '--disable-component-extensions-with-background-pages',
            '--disable-features=TranslateUI',
            '--disable-features=BlinkGenPropertyTrees',
            '--disable-logging',
            '--silent',
            '--log-level=3',
            '--user-data-dir=/tmp/chrome_dev_test',
            '--remote-debugging-port=0'
        ]

        # 添加稳定性参数
        for arg in stability_args:
            options.add_argument(arg)

        logger.info(f"添加了 {len(stability_args)} 个稳定性参数")

        # 添加配置的启动参数
        for arg in self.config.chrome_options:
            options.add_argument(arg)
        logger.info(f"添加了 {len(self.config.chrome_options)} 个配置参数")

        # 无头模式配置
        if self.headless:
            options.add_argument('--headless=new')  # 使用新的headless模式
            logger.info("启用无头模式")

        # 设置浏览器二进制路径（如果配置了）
        if self.config.chrome_binary_path:
            options.binary_location = self.config.chrome_binary_path
            logger.info(f"使用指定的Chrome路径: {self.config.chrome_binary_path}")

        # 设置偏好配置
        options.add_experimental_option("prefs", self.config.chrome_prefs)

        # 设置其他实验性选项
        options.add_experimental_option("useAutomationExtension", False)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_argument("--disable-blink-features=AutomationControlled")

        # 创建服务对象
        logger.info("正在准备ChromeDriver...")
        if self.config.chrome_driver_path:
            # 使用指定的驱动路径
            service = ChromeService(self.config.chrome_driver_path)
            logger.info(f"使用指定的ChromeDriver路径: {self.config.chrome_driver_path}")
        else:
            # 使用webdriver-manager自动下载
            logger.info("正在下载/检查ChromeDriver...")
            try:
                driver_path = ChromeDriverManager().install()
                service = ChromeService(driver_path)
                logger.info(f"ChromeDriver路径: {driver_path}")
            except Exception as e:
                logger.error(f"ChromeDriver下载失败: {e}")
                raise

        # 启动浏览器
        logger.info("正在启动Chrome浏览器...")
        try:
            # 使用超时机制启动浏览器
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("浏览器启动超时")

            # 设置超时信号（仅在非Windows系统上）
            import platform
            if platform.system() != 'Windows':
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(60)  # 60秒超时

            self.driver = webdriver.Chrome(service=service, options=options)

            # 取消超时信号
            if platform.system() != 'Windows':
                signal.alarm(0)

            logger.info("Chrome浏览器启动成功")

            # 设置超时时间
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            logger.info("浏览器超时设置完成")

        except TimeoutError as e:
            logger.error(f"Chrome浏览器启动超时: {e}")
            raise
        except Exception as e:
            logger.error(f"Chrome浏览器启动失败: {e}")
            # 如果是Windows系统，提供额外的调试信息
            if platform.system() == 'Windows':
                logger.error("Windows系统浏览器启动失败，可能的原因：")
                logger.error("1. Chrome进程被防火墙阻止")
                logger.error("2. Chrome版本与ChromeDriver不兼容")
                logger.error("3. 系统资源不足")
                logger.error("4. Chrome安装路径问题")
            raise
        
    def _start_firefox(self):
        """启动Firefox浏览器"""
        options = webdriver.FirefoxOptions()

        # 添加配置的启动参数
        for arg in self.config.firefox_options:
            options.add_argument(arg)

        # 无头模式配置
        if self.headless:
            options.add_argument('--headless')

        # 设置浏览器二进制路径（如果配置了）
        if self.config.firefox_binary_path:
            options.binary_location = self.config.firefox_binary_path
            logger.info(f"使用指定的Firefox路径: {self.config.firefox_binary_path}")

        # 创建服务对象
        if self.config.firefox_driver_path:
            service = FirefoxService(self.config.firefox_driver_path)
            logger.info(f"使用指定的GeckoDriver路径: {self.config.firefox_driver_path}")
        else:
            service = FirefoxService(GeckoDriverManager().install())

        self.driver = webdriver.Firefox(service=service, options=options)

    def _start_edge(self):
        """启动Edge浏览器"""
        options = webdriver.EdgeOptions()

        # 添加配置的启动参数
        for arg in self.config.edge_options:
            options.add_argument(arg)

        # 无头模式配置
        if self.headless:
            options.add_argument('--headless')

        # 设置浏览器二进制路径（如果配置了）
        if self.config.edge_binary_path:
            options.binary_location = self.config.edge_binary_path
            logger.info(f"使用指定的Edge路径: {self.config.edge_binary_path}")

        # 创建服务对象
        if self.config.edge_driver_path:
            service = EdgeService(self.config.edge_driver_path)
            logger.info(f"使用指定的EdgeDriver路径: {self.config.edge_driver_path}")
        else:
            service = EdgeService(EdgeChromiumDriverManager().install())

        self.driver = webdriver.Edge(service=service, options=options)
    
    def navigate_to(self, url: str) -> bool:
        """
        导航到指定URL
        参数: url - 目标URL
        返回: 导航是否成功
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return False
                
            self.driver.get(url)
            logger.info(f"导航到: {url}")
            return True
            
        except Exception as e:
            logger.error(f"导航失败: {e}")
            return False
    
    def find_element(self, by: By, value: str, timeout: Optional[int] = None) -> Optional[Any]:
        """
        查找页面元素
        参数: 
            by - 查找方式 (By.ID, By.CLASS_NAME等)
            value - 查找值
            timeout - 超时时间(秒)，默认使用全局配置
        返回: 找到的元素或None
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
            
            wait_time = timeout or self.timeout
            wait = WebDriverWait(self.driver, wait_time)
            element = wait.until(EC.presence_of_element_located((by, value)))
            
            return element
            
        except Exception as e:
            logger.error(f"查找元素失败 {by}='{value}': {e}")
            return None
    
    def get_page_source(self) -> Optional[str]:
        """
        获取当前页面源码
        返回: 页面源码或None
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
                
            return self.driver.page_source
            
        except Exception as e:
            logger.error(f"获取页面源码失败: {e}")
            return None
    
    def execute_script(self, script: str) -> Any:
        """
        执行JavaScript脚本
        参数: script - JavaScript代码
        返回: 脚本执行结果
        """
        try:
            if not self.driver:
                logger.error("浏览器未启动")
                return None
                
            return self.driver.execute_script(script)
            
        except Exception as e:
            logger.error(f"执行脚本失败: {e}")
            return None
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("浏览器已关闭")
                
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
    
    def is_browser_running(self) -> bool:
        """
        检查浏览器是否正在运行
        返回: 浏览器运行状态
        """
        try:
            if not self.driver:
                return False
            
            # 尝试获取当前URL来检查浏览器状态
            _ = self.driver.current_url
            return True
            
        except Exception:
            return False
