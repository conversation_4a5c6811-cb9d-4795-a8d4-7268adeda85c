"""
测试改进后的输入选择器功能
验证多种选择器类型和网站特定优化
"""

import sys
import os
import time
from loguru import logger

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.browser_controller.browser_manager import BrowserManager
from src.browser_controller.input_handler import InputHandler


def test_selector_detection():
    """测试选择器类型检测"""
    print("🔍 测试选择器类型检测")
    print("-" * 40)
    
    browser_manager = BrowserManager()
    input_handler = InputHandler(browser_manager)
    
    test_selectors = [
        ('#chat-input', 'id'),
        ('.chat-input', 'class'),
        ('textarea[placeholder="输入"]', 'css'),
        ('//textarea[@placeholder="输入"]', 'xpath'),
        ('//div[contains(@class, "input")]', 'xpath'),
        ('.//*[@id="input"]', 'xpath'),
        ('name=username', 'name'),
        ('input[type="text"]', 'css'),
    ]
    
    for selector, expected_type in test_selectors:
        detected_type = input_handler._detect_selector_type(selector)
        status = "✅" if detected_type == expected_type else "❌"
        print(f"{status} {selector:<35} -> {detected_type:<8} (期望: {expected_type})")
    
    print()


def test_website_specific_selectors():
    """测试网站特定选择器"""
    print("🌐 测试网站特定选择器")
    print("-" * 40)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = InputHandler(browser_manager)
    
    # 测试不同网站的选择器
    test_urls = [
        ("https://chat.openai.com", "ChatGPT"),
        ("https://claude.ai", "Claude"),
        ("https://bard.google.com", "Google Bard"),
        ("https://space.coze.cn/task/123", "Coze"),
        ("https://tongyi.aliyun.com", "通义千问"),
        ("https://yiyan.baidu.com", "文心一言"),
        ("https://example.com", "未知网站"),
    ]
    
    for url, name in test_urls:
        try:
            print(f"\n📍 测试 {name}: {url}")
            
            # 导航到测试URL
            browser_manager.driver.get("data:text/html,<html><head><title>Test</title></head><body><h1>Test Page</h1></body></html>")
            browser_manager.driver.execute_script(f"window.history.replaceState(null, null, '{url}');")
            
            # 获取网站特定选择器
            selectors = input_handler.get_website_specific_selectors()
            
            print(f"   选择器数量: {len(selectors)}")
            print(f"   前3个选择器:")
            for i, selector in enumerate(selectors[:3]):
                selector_type = input_handler._detect_selector_type(selector)
                print(f"     {i+1}. {selector} ({selector_type})")
            
            if len(selectors) > 3:
                print(f"     ... 还有 {len(selectors) - 3} 个选择器")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    # 关闭浏览器
    browser_manager.driver.quit()
    print("\n✅ 网站特定选择器测试完成")
    return True


def test_input_ready_with_selectors():
    """测试输入准备就绪功能"""
    print("⏳ 测试输入准备就绪功能")
    print("-" * 40)
    
    browser_manager = BrowserManager()
    
    # 启动浏览器
    if not browser_manager.start_browser():
        print("❌ 浏览器启动失败")
        return False
    
    input_handler = InputHandler(browser_manager)
    
    # 创建测试页面
    test_html = """
    <html>
    <head><title>Input Test</title></head>
    <body>
        <h1>输入框测试页面</h1>
        
        <!-- CSS选择器测试 -->
        <div>
            <label>CSS ID选择器:</label>
            <input type="text" id="test-input-id" placeholder="CSS ID测试">
        </div>
        
        <div>
            <label>CSS Class选择器:</label>
            <input type="text" class="test-input-class" placeholder="CSS Class测试">
        </div>
        
        <div>
            <label>Textarea:</label>
            <textarea id="test-textarea" placeholder="Textarea测试"></textarea>
        </div>
        
        <!-- 可编辑div -->
        <div>
            <label>可编辑Div:</label>
            <div contenteditable="true" id="test-editable" style="border:1px solid #ccc; padding:5px;">可编辑内容测试</div>
        </div>
        
        <!-- 隐藏的输入框 -->
        <div>
            <input type="text" id="hidden-input" placeholder="隐藏输入框" style="display:none;">
        </div>
    </body>
    </html>
    """
    
    try:
        # 加载测试页面
        browser_manager.driver.get("data:text/html," + test_html)
        time.sleep(1)
        
        # 测试不同类型的选择器
        test_cases = [
            ("CSS ID选择器", "#test-input-id"),
            ("CSS Class选择器", ".test-input-class"),
            ("Textarea选择器", "#test-textarea"),
            ("XPath选择器", "//input[@id='test-input-id']"),
            ("XPath Textarea", "//textarea[@id='test-textarea']"),
            ("可编辑Div", "#test-editable"),
            ("不存在的选择器", "#non-existent"),
            ("隐藏的输入框", "#hidden-input"),
        ]
        
        for test_name, selector in test_cases:
            print(f"\n🧪 测试 {test_name}: {selector}")
            
            start_time = time.time()
            ready = input_handler.wait_for_input_ready(selector, timeout=3, use_website_specific=False)
            end_time = time.time()
            
            status = "✅" if ready else "❌"
            print(f"   {status} 结果: {'准备就绪' if ready else '未找到/不可用'} (耗时: {end_time - start_time:.2f}秒)")
            
            if ready:
                # 尝试输入测试文本
                success = input_handler.input_text(f"测试文本 - {test_name}", selector, use_website_specific=False)
                print(f"   {'✅' if success else '❌'} 输入测试: {'成功' if success else '失败'}")
        
        print("\n✅ 输入准备就绪测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 关闭浏览器
        browser_manager.driver.quit()


def main():
    """主函数"""
    print("🚀 输入选择器功能测试")
    print("=" * 60)
    
    try:
        # 1. 测试选择器类型检测
        test_selector_detection()
        
        # 2. 测试网站特定选择器
        if test_website_specific_selectors():
            print("✅ 网站特定选择器测试通过")
        else:
            print("❌ 网站特定选择器测试失败")
        
        # 3. 测试输入准备就绪功能
        if test_input_ready_with_selectors():
            print("✅ 输入准备就绪测试通过")
        else:
            print("❌ 输入准备就绪测试失败")
        
        print("\n" + "=" * 60)
        print("🎉 输入选择器功能测试完成！")
        
        print("\n💡 改进总结:")
        print("✅ 支持多种选择器类型（CSS、XPath、ID、Class等）")
        print("✅ 智能选择器类型检测")
        print("✅ 网站特定选择器优化")
        print("✅ 针对主流AI网站的专门优化")
        print("✅ 兼容性和稳定性提升")
        
        print("\n🔧 支持的网站:")
        print("• ChatGPT (chat.openai.com)")
        print("• Claude (claude.ai)")
        print("• Google Bard/Gemini")
        print("• Coze 扣子 (coze.cn)")
        print("• 通义千问 (tongyi.aliyun.com)")
        print("• 文心一言 (yiyan.baidu.com)")
        print("• 通用网站（使用默认选择器）")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
