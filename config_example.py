"""
配置示例文件 - 展示如何配置Browser AI项目
复制此文件为config_local.py并根据需要修改配置
"""

# 这个文件展示了如何通过代码方式配置Browser AI项目
# 你也可以通过.env文件或环境变量来配置

from config import Settings, BrowserConfig, InputConfig, ResponseConfig

def create_custom_config():
    """
    创建自定义配置示例
    
    使用思路：
    1. 根据你的环境和需求调整配置
    2. 特别注意浏览器路径配置
    3. 可以针对不同的AI网站调整选择器
    """
    
    # 浏览器配置示例
    browser_config = BrowserConfig(
        # 基本配置
        browser_type="chrome",  # 或 "firefox", "edge"
        browser_headless=False,  # 设为True可以无头运行
        browser_timeout=30,
        browser_window_size="1920,1080",
        
        # Windows系统浏览器路径示例
        chrome_binary_path=r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        firefox_binary_path=r"C:\Program Files\Mozilla Firefox\firefox.exe",
        edge_binary_path=r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
        
        # Linux系统浏览器路径示例
        # chrome_binary_path="/usr/bin/google-chrome",
        # firefox_binary_path="/usr/bin/firefox",
        # edge_binary_path="/usr/bin/microsoft-edge",
        
        # macOS系统浏览器路径示例
        # chrome_binary_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        # firefox_binary_path="/Applications/Firefox.app/Contents/MacOS/firefox",
        
        # 驱动程序路径（通常不需要设置，webdriver-manager会自动处理）
        # chrome_driver_path=r"D:\drivers\chromedriver.exe",
        
        # Chrome启动参数自定义
        chrome_options=[
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-blink-features=AutomationControlled",
            "--disable-extensions",
            "--disable-images",  # 禁用图片加载以提高速度
            "--disable-javascript",  # 如果不需要JS可以禁用
        ],
        
        # Chrome偏好设置
        chrome_prefs={
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.media_stream": 2,
        },
        
        # 超时配置
        page_load_timeout=30,
        implicit_wait=10,
        script_timeout=30,
        
        # 重试配置
        browser_start_retry_count=3,
        browser_start_retry_delay=2
    )
    
    # 输入配置示例 - 针对不同AI网站优化
    input_config = InputConfig(
        # ChatGPT网站的输入框选择器
        input_selectors=[
            'textarea[data-id="root"]',  # ChatGPT主输入框
            'textarea[placeholder*="Message"]',
            'textarea[placeholder*="输入"]',
            'input[type="text"]',
            'textarea',
            '.chat-input',
            '#prompt-textarea',
        ],
        
        # 输入行为配置
        input_delay=0.05,  # 稍微快一点的输入速度
        clear_before_input=True,
        input_retry_count=3,
        input_retry_delay=1.0,
        
        # 提交按钮选择器
        submit_button_selectors=[
            'button[data-testid="send-button"]',  # ChatGPT发送按钮
            'button[aria-label="Send prompt"]',
            'button:contains("发送")',
            'button:contains("Send")',
            '.send-button',
            '[data-testid="send-button"]',
        ]
    )
    
    # 响应配置示例 - 针对不同AI网站优化
    response_config = ResponseConfig(
        # ChatGPT网站的响应选择器
        response_selectors=[
            '[data-message-author-role="assistant"]',  # ChatGPT响应
            '.markdown',
            '.message-content',
            '.response-content',
            '.ai-message',
            '.assistant-message',
        ],
        
        # 响应等待配置
        response_timeout=120,  # 增加超时时间，AI回复可能较慢
        response_check_interval=0.3,  # 更频繁的检查
        response_stable_time=3.0,  # 等待响应稳定的时间
        
        # 响应过滤配置
        min_response_length=10,
        max_response_length=100000,
        ignore_empty_responses=True,
        
        # 响应处理配置
        clean_response_text=True,
        remove_html_tags=True,
        normalize_whitespace=True
    )
    
    # 主配置
    settings = Settings(
        # API配置
        api_host="0.0.0.0",
        api_port=8000,
        api_title="Browser AI API",
        api_version="1.0.0",
        
        # AI模型配置
        model_name="gpt-4",  # 可以改为你想要的模型名称
        max_tokens=4096,
        temperature=0.7,
        
        # 日志配置
        log_level="INFO",
        log_file="browser_ai.log",
        log_rotation="50 MB",
        log_retention="30 days",
        
        # 性能配置
        max_concurrent_requests=5,  # 根据你的硬件调整
        request_timeout=600,  # 10分钟超时
        
        # 子配置
        browser=browser_config,
        input=input_config,
        response=response_config
    )
    
    return settings


# 不同AI网站的配置示例
def get_chatgpt_config():
    """ChatGPT网站专用配置"""
    return create_custom_config()


def get_claude_config():
    """Claude网站专用配置"""
    config = create_custom_config()
    
    # 针对Claude网站的特殊配置
    config.input.input_selectors = [
        'div[contenteditable="true"]',  # Claude输入框
        '.ProseMirror',
        '[data-testid="chat-input"]',
    ] + config.input.input_selectors
    
    config.response.response_selectors = [
        '[data-testid="conversation-turn-content"]',  # Claude响应
        '.font-claude-message',
        '.prose',
    ] + config.response.response_selectors
    
    return config


def get_bard_config():
    """Google Bard网站专用配置"""
    config = create_custom_config()
    
    # 针对Bard网站的特殊配置
    config.input.input_selectors = [
        'rich-textarea[placeholder*="Enter a prompt"]',
        '.ql-editor',
        '[data-test-id="input-area"]',
    ] + config.input.input_selectors
    
    config.response.response_selectors = [
        '[data-test-id="bot-response"]',
        '.model-response-text',
        '.response-container',
    ] + config.response.response_selectors
    
    return config


# 使用示例
if __name__ == "__main__":
    # 创建自定义配置
    custom_settings = create_custom_config()
    
    print("自定义配置创建成功！")
    print(f"浏览器类型: {custom_settings.browser.browser_type}")
    print(f"Chrome路径: {custom_settings.browser.chrome_binary_path}")
    print(f"输入选择器数量: {len(custom_settings.input.input_selectors)}")
    print(f"响应选择器数量: {len(custom_settings.response.response_selectors)}")
    
    # 你可以将这个配置保存到文件或直接使用
    # 例如：替换config.py中的全局settings实例
