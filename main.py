"""
主应用程序入口
启动FastAPI服务，提供OpenAI兼容的浏览器AI接口
"""

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from loguru import logger

from config import settings
from src.utils.logger_config import setup_logger
from src.api.chat_api import router as chat_router
from src.api.browser_api import router as browser_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期管理
    
    使用思路：
    1. 在应用启动时初始化系统组件
    2. 在应用关闭时清理资源
    3. 确保系统稳定运行
    """
    # 启动时的初始化
    logger.info("正在启动Browser AI服务...")
    
    # 设置日志系统
    setup_logger()
    
    logger.info(f"服务配置:")
    logger.info(f"  - API地址: {settings.api_host}:{settings.api_port}")
    logger.info(f"  - 浏览器类型: {settings.browser_type}")
    logger.info(f"  - 无头模式: {settings.browser_headless}")
    logger.info(f"  - 调试模式: {settings.debug}")
    
    yield
    
    # 关闭时的清理
    logger.info("正在关闭Browser AI服务...")
    
    # 这里可以添加清理浏览器实例等操作
    try:
        # 导入并清理浏览器资源
        from src.api.chat_api import browser_manager as chat_browser
        from src.api.browser_api import browser_manager as api_browser
        
        if chat_browser:
            chat_browser.close_browser()
            logger.info("聊天API浏览器实例已关闭")
            
        if api_browser:
            api_browser.close_browser()
            logger.info("浏览器API实例已关闭")
            
    except Exception as e:
        logger.error(f"清理浏览器资源时发生错误: {e}")
    
    logger.info("Browser AI服务已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description="基于浏览器控制的AI接口服务，提供OpenAI兼容的聊天完成API",
    lifespan=lifespan,
    debug=settings.debug
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器
    
    使用思路：
    1. 统一处理未捕获的异常
    2. 返回标准化的错误响应
    3. 记录详细的错误日志
    """
    logger.error(f"全局异常: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "message": "内部服务器错误",
                "type": "internal_server_error",
                "details": str(exc) if settings.debug else "请联系管理员"
            }
        }
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """
    健康检查端点
    
    返回: 服务健康状态
    
    使用思路：
    1. 用于监控服务是否正常运行
    2. 检查关键组件的状态
    3. 支持负载均衡器的健康检查
    
    使用例子：
    GET /health
    返回: {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}
    """
    try:
        from datetime import datetime
        
        # 这里可以添加更多的健康检查逻辑
        # 比如检查浏览器状态、数据库连接等
        
        return {
            "status": "healthy",
            "service": settings.api_title,
            "version": settings.api_version,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "config": {
                "browser_type": settings.browser_type,
                "headless": settings.browser_headless,
                "debug": settings.debug
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不健康")


# 根路径重定向到文档
@app.get("/")
async def root():
    """
    根路径处理
    
    返回: 服务信息和文档链接
    
    使用思路：
    1. 提供服务的基本信息
    2. 引导用户查看API文档
    
    使用例子：
    GET /
    返回服务欢迎信息和文档链接
    """
    return {
        "message": f"欢迎使用 {settings.api_title}",
        "version": settings.api_version,
        "description": "基于浏览器控制的AI接口服务",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
        "openapi_url": "/openapi.json",
        "endpoints": {
            "chat_completions": "/v1/chat/completions",
            "browser_status": "/browser/status",
            "browser_navigate": "/browser/navigate",
            "health_check": "/health"
        }
    }


# 注册路由
app.include_router(chat_router, tags=["Chat API"])
app.include_router(browser_router, tags=["Browser Control"])


def main():
    """
    主函数 - 启动服务
    
    使用思路：
    1. 配置并启动uvicorn服务器
    2. 支持热重载（开发模式）
    3. 处理启动参数
    
    使用例子：
    python main.py
    """
    try:
        logger.info("启动Browser AI服务...")
        
        # 启动配置
        uvicorn_config = {
            "app": "main:app",
            "host": settings.api_host,
            "port": settings.api_port,
            "reload": settings.debug,  # 开发模式下启用热重载
            "log_level": settings.log_level.lower(),
            "access_log": True,
        }
        
        # 如果是生产环境，可以添加更多配置
        if not settings.debug:
            uvicorn_config.update({
                "workers": 1,  # 浏览器控制通常需要单进程
                "loop": "asyncio",
            })
        
        logger.info(f"服务将在 http://{settings.api_host}:{settings.api_port} 启动")
        logger.info(f"API文档地址: http://{settings.api_host}:{settings.api_port}/docs")
        
        # 启动服务
        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动服务失败: {e}")
        raise


if __name__ == "__main__":
    main()
