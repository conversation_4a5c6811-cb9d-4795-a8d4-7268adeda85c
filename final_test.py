"""
Browser AI 最终功能测试脚本
验证浏览器启动修复后的完整功能
"""

import requests
import time
import json
import webbrowser
from typing import Dict, Any


class BrowserAIFinalTest:
    """Browser AI 最终测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        """初始化测试类"""
        self.base_url = base_url.rstrip('/')
        self.timeout = 30
        
    def test_service_health(self) -> bool:
        """测试服务健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务健康: {data['status']}")
                print(f"   版本: {data['version']}")
                print(f"   浏览器: {data['config']['browser_type']}")
                print(f"   无头模式: {data['config']['headless']}")
                return True
            else:
                print(f"❌ 服务健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False
    
    def test_browser_status(self) -> Dict[str, Any]:
        """测试浏览器状态"""
        try:
            response = requests.get(f"{self.base_url}/browser/status", timeout=30)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 浏览器状态:")
                print(f"   运行中: {data['running']}")
                print(f"   类型: {data['browser_type']}")
                print(f"   无头模式: {data['headless']}")
                print(f"   当前URL: {data.get('current_url', 'N/A')[:80]}...")
                return data
            else:
                print(f"❌ 浏览器状态检查失败: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ 浏览器状态检查异常: {e}")
            return {}
    
    def test_navigation(self, url: str = "https://www.baidu.com") -> bool:
        """测试浏览器导航"""
        try:
            print(f"🌐 测试导航到: {url}")
            data = {"url": url, "wait_for_load": True}
            response = requests.post(
                f"{self.base_url}/browser/navigate",
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 导航成功: {result['message']}")
                    return True
                else:
                    print(f"❌ 导航失败: {result}")
                    return False
            else:
                print(f"❌ 导航请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 导航异常: {e}")
            return False
    
    def test_page_source(self) -> bool:
        """测试页面源码获取"""
        try:
            print("📄 测试页面源码获取...")
            response = requests.get(f"{self.base_url}/browser/page-source", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                source = data.get('source', '')
                if source and len(source) > 100:
                    print(f"✅ 页面源码获取成功，长度: {len(source)} 字符")
                    print(f"   源码预览: {source[:100]}...")
                    return True
                else:
                    print(f"⚠️ 页面源码为空或过短")
                    return False
            else:
                print(f"❌ 页面源码获取失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 页面源码获取异常: {e}")
            return False
    
    def test_ai_website_status(self) -> Dict[str, Any]:
        """测试AI网站状态"""
        try:
            print("🤖 测试AI网站状态...")
            response = requests.get(f"{self.base_url}/ai-website/status", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI网站状态:")
                print(f"   当前网站: {data.get('current_website', 'None')}")
                print(f"   当前URL: {data.get('current_url', 'None')}")
                print(f"   默认网站: {data.get('default_website', 'None')}")
                print(f"   自动导航: {data.get('auto_navigate', False)}")
                return data
            else:
                print(f"❌ AI网站状态获取失败: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ AI网站状态获取异常: {e}")
            return {}
    
    def test_available_ai_websites(self) -> bool:
        """测试可用AI网站列表"""
        try:
            print("📋 测试可用AI网站列表...")
            response = requests.get(f"{self.base_url}/ai-website/available", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                websites = data.get('websites', [])
                print(f"✅ 可用AI网站 ({len(websites)}个):")
                for site in websites:
                    current_mark = " ⭐" if site.get('current') else ""
                    print(f"   • {site['name']} ({site['key']}){current_mark}")
                return len(websites) > 0
            else:
                print(f"❌ 获取可用网站失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取可用网站异常: {e}")
            return False
    
    def test_web_interface(self) -> bool:
        """测试网页界面"""
        try:
            print("🌐 测试网页界面...")
            response = requests.get(f"{self.base_url}/chat", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                if "Browser AI 对话测试" in content:
                    print("✅ 网页界面正常")
                    return True
                else:
                    print("⚠️ 网页界面内容异常")
                    return False
            else:
                print(f"❌ 网页界面访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 网页界面测试异常: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🚀 Browser AI 完整功能测试")
        print("=" * 60)
        
        test_results = {}
        
        # 1. 服务健康检查
        print("\n1. 服务健康检查...")
        test_results['health'] = self.test_service_health()
        
        if not test_results['health']:
            print("❌ 服务未启动，测试终止")
            return False
        
        # 2. 浏览器状态检查
        print("\n2. 浏览器状态检查...")
        browser_status = self.test_browser_status()
        test_results['browser_status'] = bool(browser_status.get('running'))
        
        # 3. 导航功能测试
        print("\n3. 导航功能测试...")
        test_results['navigation'] = self.test_navigation()
        
        # 4. 页面源码获取测试
        print("\n4. 页面源码获取测试...")
        test_results['page_source'] = self.test_page_source()
        
        # 5. AI网站状态测试
        print("\n5. AI网站状态测试...")
        ai_status = self.test_ai_website_status()
        test_results['ai_website_status'] = bool(ai_status)
        
        # 6. 可用AI网站列表测试
        print("\n6. 可用AI网站列表测试...")
        test_results['ai_websites_list'] = self.test_available_ai_websites()
        
        # 7. 网页界面测试
        print("\n7. 网页界面测试...")
        test_results['web_interface'] = self.test_web_interface()
        
        # 测试结果汇总
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("\n🎉 Browser AI 功能测试成功！")
            print("\n💡 现在您可以：")
            print("1. 访问网页界面进行对话测试")
            print("2. 使用API进行程序化调用")
            print("3. 在不同AI网站间切换")
            print("4. 测试完整的AI对话功能")
            
            # 打开网页界面
            try:
                webbrowser.open(f"{self.base_url}/chat")
                print(f"\n🌐 网页界面已在浏览器中打开: {self.base_url}/chat")
            except:
                print(f"\n🌐 请手动访问: {self.base_url}/chat")
            
            return True
        else:
            print("\n⚠️ 部分功能测试失败，请检查相关配置")
            return False


def main():
    """主函数"""
    try:
        # 创建测试实例
        tester = BrowserAIFinalTest("http://localhost:8002")
        
        # 运行完整测试
        success = tester.run_complete_test()
        
        if success:
            print("\n🎊 恭喜！Browser AI 已成功修复并正常运行！")
            print("\n🔧 修复总结:")
            print("✅ 解决了Chrome浏览器启动卡住的问题")
            print("✅ 优化了Chrome启动参数配置")
            print("✅ 添加了浏览器启动重试机制")
            print("✅ 改善了错误处理和日志输出")
            print("✅ 验证了所有核心功能正常工作")
        else:
            print("\n⚠️ 测试未完全通过，但主要功能应该可用")
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
