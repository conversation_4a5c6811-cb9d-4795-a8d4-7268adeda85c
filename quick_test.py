"""
快速测试脚本 - 验证Browser AI项目的基本功能
"""

import requests
import time
import json


def test_health():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data['status']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def test_root():
    """测试根路径"""
    print("\n🔍 测试根路径...")
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 根路径访问成功: {data['message']}")
            return True
        else:
            print(f"❌ 根路径访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 根路径访问异常: {e}")
        return False


def test_browser_status():
    """测试浏览器状态接口"""
    print("\n🔍 测试浏览器状态接口...")
    try:
        print("   注意：首次调用可能需要下载浏览器驱动，请耐心等待...")
        response = requests.get("http://localhost:8000/browser/status", timeout=120)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 浏览器状态获取成功:")
            print(f"   - 运行状态: {data['running']}")
            print(f"   - 浏览器类型: {data['browser_type']}")
            print(f"   - 无头模式: {data['headless']}")
            if data.get('current_url'):
                print(f"   - 当前URL: {data['current_url']}")
            return True
        else:
            print(f"❌ 浏览器状态获取失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 浏览器状态获取异常: {e}")
        return False


def test_browser_navigate():
    """测试浏览器导航功能"""
    print("\n🔍 测试浏览器导航功能...")
    try:
        payload = {
            "url": "https://www.baidu.com",
            "wait_for_load": True
        }
        response = requests.post(
            "http://localhost:8000/browser/navigate",
            json=payload,
            timeout=60
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 浏览器导航成功: {payload['url']}")
                return True
            else:
                print(f"❌ 浏览器导航失败: {data}")
                return False
        else:
            print(f"❌ 浏览器导航请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 浏览器导航异常: {e}")
        return False


def test_browser_input():
    """测试浏览器输入功能"""
    print("\n🔍 测试浏览器输入功能...")
    try:
        payload = {
            "text": "Browser AI 测试",
            "submit": False
        }
        response = requests.post(
            "http://localhost:8000/browser/input",
            json=payload,
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 浏览器输入成功: {payload['text']}")
                return True
            else:
                print(f"❌ 浏览器输入失败: {data}")
                return False
        else:
            print(f"❌ 浏览器输入请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 浏览器输入异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Browser AI 快速测试")
    print("=" * 50)
    
    # 测试列表
    tests = [
        ("健康检查", test_health),
        ("根路径", test_root),
        ("浏览器状态", test_browser_status),
        ("浏览器导航", test_browser_navigate),
        ("浏览器输入", test_browser_input),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except KeyboardInterrupt:
            print(f"\n⚠️ 测试被用户中断")
            break
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生未预期错误: {e}")
            results[test_name] = False
        
        # 测试间隔
        time.sleep(1)
    
    # 生成测试报告
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    success_rate = (passed / total * 100) if total > 0 else 0
    print(f"测试通过率: {passed}/{total} ({success_rate:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试都通过了！Browser AI项目运行正常。")
        print("\n💡 接下来你可以:")
        print("   1. 访问 http://localhost:8000/docs 查看完整API文档")
        print("   2. 运行 python example_usage.py 查看使用示例")
        print("   3. 运行 python tests/test_api.py 进行完整测试")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查服务配置和环境。")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
